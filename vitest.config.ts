import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'happy-dom',
    globals: true,
    setupFiles: ['./resources/js/tests/setup.ts'],
    include: ['resources/js/**/*.test.{ts,tsx}'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'resources/js/tests/'],
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './resources/js'),
      'ziggy-js': resolve(__dirname, './vendor/tightenco/ziggy'),
    },
  },
});
