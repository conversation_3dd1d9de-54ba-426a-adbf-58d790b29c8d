# IPTV Manager

A Laravel 12 + React/TypeScript application for importing, validating, managing, and streaming M3U playlists.

## Features

- Playlist management: view, refetch, revalidate, and remove playlists with option to delete associated streams
- Import functionality: bulk URL imports, bulk M3U file uploads, revalidation for identical URLs
- Background processing: automatically queue all imports for sequential processing
- Stream validation scheduling with options to schedule automatic validation, refetch, or both every X hours
- Channel clustering: normalize names, group identical normalized names, show cluster badges, inherit the most frequent category, and mark clusters as online if any member stream is online
- Export functionality with GitHub integration and preferred channel lists

## Development

### Requirements

- Docker
- Docker Compose
- Laravel Sail

### Setup

1. Clone the repository
2. Run `composer install`
3. Copy `.env.example` to `.env` and configure your environment
4. Run `sail up -d` to start the Docker containers
5. Run `sail artisan migrate` to create the database tables
6. Run `sail npm install` to install the frontend dependencies
7. Run `sail npm run dev` to compile the frontend assets

### Testing

To run the tests, use the provided script:

```bash
./run-tests.sh
```

This script runs the tests with process isolation to avoid Mockery issues.

You can also run specific tests:

```bash
./run-tests.sh --filter=TestName
```

## License

This project is licensed under the MIT License.
