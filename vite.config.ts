import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.tsx'],
            ssr: 'resources/js/ssr.tsx',
            refresh: true,
        }),
        react(),
        tailwindcss(),
    ],
    esbuild: {
        jsx: 'automatic',
    },
    resolve: {
        alias: {
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    // Vendor chunks
                    'vendor-react': ['react', 'react-dom'],
                    'vendor-ui': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
                    'vendor-charts': ['recharts', 'd3-scale', 'd3-array'],
                    'vendor-utils': ['date-fns', 'lodash', 'axios'],
                    'vendor-icons': ['lucide-react'],

                    // Feature chunks
                    'pages-auth': [
                        'resources/js/pages/auth/login.tsx',
                        'resources/js/pages/auth/register.tsx',
                        'resources/js/pages/auth/forgot-password.tsx',
                        'resources/js/pages/auth/reset-password.tsx',
                    ],
                    'pages-streams': [
                        'resources/js/pages/streams/index.tsx',
                        'resources/js/pages/streams/show.tsx',
                        'resources/js/pages/streams/create.tsx',
                        'resources/js/pages/streams/edit.tsx',
                    ],
                    'pages-playlists': [
                        'resources/js/pages/playlists/index.tsx',
                        'resources/js/pages/playlists/show.tsx',
                        'resources/js/pages/playlists/create.tsx',
                        'resources/js/pages/playlists/edit.tsx',
                        'resources/js/pages/playlists/editor.tsx',
                    ],
                    'pages-settings': [
                        'resources/js/pages/settings/profile.tsx',
                        'resources/js/pages/settings/password.tsx',
                        'resources/js/pages/settings/appearance.tsx',
                        'resources/js/pages/settings/preferences.tsx',
                        'resources/js/pages/settings/github.tsx',
                        'resources/js/pages/settings/proxy.tsx',
                        'resources/js/pages/settings/stream.tsx',
                    ],
                    'components-video': [
                        'resources/js/components/video-player.tsx',
                        'resources/js/pages/player/show.tsx',
                    ],
                },
            },
        },
        chunkSizeWarningLimit: 1000, // Increase warning limit
        sourcemap: false, // Disable sourcemaps in production for smaller builds
    },
    optimizeDeps: {
        include: [
            'react',
            'react-dom',
            '@inertiajs/react',
            'axios',
            'lodash',
            'date-fns',
            'lucide-react',
        ],
    },
});
