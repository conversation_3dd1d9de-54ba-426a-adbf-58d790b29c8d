<?php

namespace Database\Factories;

use App\Models\UrlListImport;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UrlListImport>
 */
class UrlListImportFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UrlListImport::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'url' => $this->faker->url,
            'name' => $this->faker->words(3, true),
            'is_active' => $this->faker->boolean,
            'interval_hours' => $this->faker->randomElement([1, 6, 12, 24, 48, 72, 168]),
            'last_run_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now'),
            'next_run_at' => $this->faker->optional()->dateTimeBetween('now', '+1 week'),
            'metadata' => null,
        ];
    }

    /**
     * Indicate that the URL list import is active.
     *
     * @return static
     */
    public function active()
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'next_run_at' => now()->addHours($attributes['interval_hours'] ?? 24),
        ]);
    }

    /**
     * Indicate that the URL list import is inactive.
     *
     * @return static
     */
    public function inactive()
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'next_run_at' => null,
        ]);
    }

    /**
     * Indicate that the URL list import is due to run.
     *
     * @return static
     */
    public function due()
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'next_run_at' => now()->subMinutes(5),
        ]);
    }
}
