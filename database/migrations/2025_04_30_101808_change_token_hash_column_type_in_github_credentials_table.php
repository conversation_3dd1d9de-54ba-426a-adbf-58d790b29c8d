<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('github_credentials', function (Blueprint $table) {
            // Change token_hash column from VARCHAR(255) to TEXT
            $table->text('token_hash')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('github_credentials', function (Blueprint $table) {
            // Change token_hash column back to VARCHAR(255)
            $table->string('token_hash', 255)->change();
        });
    }
};
