<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('streams', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('url');
            $table->string('type')->default('hls'); // hls, rtmp, etc.
            $table->enum('status', ['pending', 'online', 'offline', 'error'])->default('pending');
            $table->json('metadata')->nullable();
            $table->timestamp('last_checked_at')->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('tvg_id')->nullable(); 
            $table->string('tvg_name')->nullable(); 
            $table->text('tvg_logo')->nullable(); // Logo URL
            $table->string('group_title')->nullable(); // Group title from M3U
            $table->integer('quality')->nullable(); // Stream quality (resolution)
            $table->integer('bitrate')->nullable(); // Stream bitrate
            $table->string('codec')->nullable(); // Stream codec
            $table->timestamps();
            $table->softDeletes();

            $table->index('status');
            $table->index('last_checked_at');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('streams');
    }
};
