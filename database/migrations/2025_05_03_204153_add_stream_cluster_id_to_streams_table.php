<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('streams', function (Blueprint $table) {
            $table->foreignId('stream_cluster_id')->nullable()->after('user_id')->constrained()->nullOnDelete();
            $table->string('normalized_name')->nullable()->after('name');
            $table->integer('cluster_priority')->nullable()->after('stream_cluster_id');

            $table->index('stream_cluster_id');
            $table->index('normalized_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('streams', function (Blueprint $table) {
            $table->dropIndex(['stream_cluster_id']);
            $table->dropIndex(['normalized_name']);
            $table->dropForeign(['stream_cluster_id']);
            $table->dropColumn('stream_cluster_id');
            $table->dropColumn('normalized_name');
            $table->dropColumn('cluster_priority');
        });
    }
};
