<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('m3u_imports', function (Blueprint $table) {
            $table->timestamp('last_refetched_at')->nullable()->after('last_fetched_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('m3u_imports', function (Blueprint $table) {
            $table->dropColumn('last_refetched_at');
        });
    }
};
