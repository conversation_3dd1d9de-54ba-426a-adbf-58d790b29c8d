<?php

namespace App\Repositories\Concerns;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use App\Services\CacheService;

/**
 * Optimizes Queries Trait
 * 
 * Provides query optimization methods for repositories including
 * caching, eager loading, and efficient pagination.
 */
trait OptimizesQueries
{
    /**
     * Cache service instance
     *
     * @var CacheService
     */
    protected $cacheService;

    /**
     * Get cache service instance
     *
     * @return CacheService
     */
    protected function getCacheService(): CacheService
    {
        if (!$this->cacheService) {
            $this->cacheService = app(CacheService::class);
        }
        
        return $this->cacheService;
    }

    /**
     * Get paginated results with caching
     *
     * @param Builder $query
     * @param int $perPage
     * @param string $cacheKey
     * @param int $cacheTtl
     * @return LengthAwarePaginator
     */
    protected function getCachedPaginated(
        Builder $query, 
        int $perPage = 15, 
        string $cacheKey = null, 
        int $cacheTtl = CacheService::TTL_MEDIUM
    ): LengthAwarePaginator {
        if (!$cacheKey) {
            return $query->paginate($perPage);
        }

        $page = request()->get('page', 1);
        $fullCacheKey = $cacheKey . ':page:' . $page . ':per_page:' . $perPage;

        return $this->getCacheService()->remember(
            $fullCacheKey,
            function () use ($query, $perPage) {
                return $query->paginate($perPage);
            },
            $cacheTtl
        );
    }

    /**
     * Get collection with caching
     *
     * @param Builder $query
     * @param string $cacheKey
     * @param int $cacheTtl
     * @return Collection
     */
    protected function getCachedCollection(
        Builder $query, 
        string $cacheKey, 
        int $cacheTtl = CacheService::TTL_MEDIUM
    ): Collection {
        return $this->getCacheService()->remember(
            $cacheKey,
            function () use ($query) {
                return $query->get();
            },
            $cacheTtl
        );
    }

    /**
     * Apply eager loading to prevent N+1 queries
     *
     * @param Builder $query
     * @param array $relations
     * @return Builder
     */
    protected function applyEagerLoading(Builder $query, array $relations): Builder
    {
        if (!empty($relations)) {
            $query->with($relations);
        }
        
        return $query;
    }

    /**
     * Apply selective columns to reduce memory usage
     *
     * @param Builder $query
     * @param array $columns
     * @return Builder
     */
    protected function applySelectiveColumns(Builder $query, array $columns = ['*']): Builder
    {
        if ($columns !== ['*']) {
            // Always include the primary key
            if (!in_array('id', $columns)) {
                $columns[] = 'id';
            }
            
            $query->select($columns);
        }
        
        return $query;
    }

    /**
     * Apply database-level search optimization
     *
     * @param Builder $query
     * @param string $term
     * @param array $searchFields
     * @return Builder
     */
    protected function applyOptimizedSearch(Builder $query, string $term, array $searchFields): Builder
    {
        if (empty($term) || empty($searchFields)) {
            return $query;
        }

        // Use database-specific full-text search if available
        if (DB::getDriverName() === 'mysql') {
            return $this->applyMySQLFullTextSearch($query, $term, $searchFields);
        }

        // Fallback to LIKE search with optimization
        return $this->applyLikeSearch($query, $term, $searchFields);
    }

    /**
     * Apply MySQL full-text search
     *
     * @param Builder $query
     * @param string $term
     * @param array $searchFields
     * @return Builder
     */
    protected function applyMySQLFullTextSearch(Builder $query, string $term, array $searchFields): Builder
    {
        // Check if full-text index exists (this would need to be configured)
        // For now, fall back to optimized LIKE search
        return $this->applyLikeSearch($query, $term, $searchFields);
    }

    /**
     * Apply optimized LIKE search
     *
     * @param Builder $query
     * @param string $term
     * @param array $searchFields
     * @return Builder
     */
    protected function applyLikeSearch(Builder $query, string $term, array $searchFields): Builder
    {
        $query->where(function ($subQuery) use ($term, $searchFields) {
            foreach ($searchFields as $field) {
                $subQuery->orWhere($field, 'like', "%{$term}%");
            }
        });

        return $query;
    }

    /**
     * Apply cursor-based pagination for large datasets
     *
     * @param Builder $query
     * @param string $cursorColumn
     * @param mixed $cursor
     * @param int $limit
     * @param string $direction
     * @return Collection
     */
    protected function applyCursorPagination(
        Builder $query, 
        string $cursorColumn = 'id', 
        $cursor = null, 
        int $limit = 50,
        string $direction = 'asc'
    ): Collection {
        if ($cursor) {
            if ($direction === 'asc') {
                $query->where($cursorColumn, '>', $cursor);
            } else {
                $query->where($cursorColumn, '<', $cursor);
            }
        }

        return $query->orderBy($cursorColumn, $direction)
                    ->limit($limit)
                    ->get();
    }

    /**
     * Get count with caching
     *
     * @param Builder $query
     * @param string $cacheKey
     * @param int $cacheTtl
     * @return int
     */
    protected function getCachedCount(
        Builder $query, 
        string $cacheKey, 
        int $cacheTtl = CacheService::TTL_LONG
    ): int {
        return $this->getCacheService()->remember(
            $cacheKey,
            function () use ($query) {
                return $query->count();
            },
            $cacheTtl
        );
    }

    /**
     * Apply batch processing for large operations
     *
     * @param Builder $query
     * @param callable $callback
     * @param int $batchSize
     * @return void
     */
    protected function processBatch(Builder $query, callable $callback, int $batchSize = 1000): void
    {
        $query->chunk($batchSize, $callback);
    }

    /**
     * Apply index hints for MySQL optimization
     *
     * @param Builder $query
     * @param string $index
     * @param string $type
     * @return Builder
     */
    protected function applyIndexHint(Builder $query, string $index, string $type = 'USE'): Builder
    {
        if (DB::getDriverName() === 'mysql') {
            $table = $query->getModel()->getTable();
            $query->from(DB::raw("{$table} {$type} INDEX ({$index})"));
        }
        
        return $query;
    }

    /**
     * Apply query optimization based on dataset size
     *
     * @param Builder $query
     * @param array $options
     * @return Builder
     */
    protected function applySmartOptimization(Builder $query, array $options = []): Builder
    {
        $estimatedRows = $options['estimated_rows'] ?? null;
        
        // For large datasets, apply specific optimizations
        if ($estimatedRows && $estimatedRows > 10000) {
            // Use covering indexes when possible
            if (isset($options['covering_columns'])) {
                $query->select($options['covering_columns']);
            }
            
            // Apply index hints if specified
            if (isset($options['index_hint'])) {
                $this->applyIndexHint($query, $options['index_hint']);
            }
        }
        
        return $query;
    }

    /**
     * Get optimized statistics
     *
     * @param string $table
     * @param array $groupBy
     * @param string $cacheKey
     * @return array
     */
    protected function getOptimizedStats(string $table, array $groupBy = [], string $cacheKey = null): array
    {
        $callback = function () use ($table, $groupBy) {
            $query = DB::table($table);
            
            if (!empty($groupBy)) {
                $query->select($groupBy)
                      ->selectRaw('COUNT(*) as count')
                      ->groupBy($groupBy);
            } else {
                $query->selectRaw('COUNT(*) as total_count');
            }
            
            return $query->get()->toArray();
        };

        if ($cacheKey) {
            return $this->getCacheService()->remember(
                $cacheKey,
                $callback,
                CacheService::TTL_LONG
            );
        }

        return $callback();
    }

    /**
     * Invalidate related caches
     *
     * @param array $cacheKeys
     * @return void
     */
    protected function invalidateRelatedCaches(array $cacheKeys): void
    {
        foreach ($cacheKeys as $key) {
            if (str_contains($key, '*')) {
                $this->getCacheService()->invalidatePattern($key);
            } else {
                cache()->forget($key);
            }
        }
    }
}
