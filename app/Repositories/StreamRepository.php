<?php

namespace App\Repositories;

use App\Models\Stream;
use App\Repositories\Concerns\OptimizesQueries;
use App\Services\CacheService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class StreamRepository extends BaseRepository
{
    use OptimizesQueries;
    /**
     * StreamRepository constructor.
     *
     * @param Stream $model
     */
    public function __construct(Stream $model)
    {
        parent::__construct($model);
    }

    /**
     * Get streams by status.
     *
     * @param string $status
     * @return Collection
     */
    public function getByStatus(string $status): Collection
    {
        return $this->findAllByField('status', $status);
    }

    /**
     * Get streams that need to be checked.
     *
     * @param int $hours
     * @return Collection
     */
    public function getNeedingCheck(int $hours = 24): Collection
    {
        return $this->model
            ->where(function ($query) {
                $query->whereNull('last_checked_at')
                    ->orWhere('last_checked_at', '<=', now()->subHours(24));
            })
            ->get();
    }

    /**
     * Get streams by user.
     *
     * @param int $userId
     * @return Collection
     */
    public function getByUser(int $userId): Collection
    {
        return $this->getByUserId($userId);
    }

    /**
     * Get streams by category.
     *
     * @param int $categoryId
     * @return Collection
     */
    public function getByCategory(int $categoryId): Collection
    {
        return $this->model->whereHas('categories', function ($query) use ($categoryId) {
            $query->where('categories.id', $categoryId);
        })->get();
    }

    /**
     * Get streams by tag.
     *
     * @param int $tagId
     * @return Collection
     */
    public function getByTag(int $tagId): Collection
    {
        return $this->model->whereHas('tags', function ($query) use ($tagId) {
            $query->where('tags.id', $tagId);
        })->get();
    }

    /**
     * Search streams by name or URL with optimization.
     *
     * @param string $term
     * @param int $userId
     * @param array $columns
     * @return Collection
     */
    public function search(string $term, int $userId = null, array $columns = ['*']): Collection
    {
        $cacheKey = 'stream:search:' . md5($term . ($userId ?? 'all'));

        return $this->getCachedCollection(
            $this->buildSearchQuery($term, $userId, $columns),
            $cacheKey,
            CacheService::TTL_SHORT
        );
    }

    /**
     * Build optimized search query
     *
     * @param string $term
     * @param int $userId
     * @param array $columns
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function buildSearchQuery(string $term, int $userId = null, array $columns = ['*'])
    {
        $query = $this->model->newQuery();

        // Apply selective columns
        $query = $this->applySelectiveColumns($query, $columns);

        // Apply user filter if provided
        if ($userId) {
            $query->where('user_id', $userId);
        }

        // Apply optimized search
        $searchFields = ['name', 'url', 'tvg_name', 'group_title'];
        $query = $this->applyOptimizedSearch($query, $term, $searchFields);

        return $query;
    }

    /**
     * Find a stream by URL and user ID.
     *
     * @param string $url
     * @param int $userId
     * @return mixed
     */
    public function findByUrlAndUser(string $url, int $userId)
    {
        return $this->findByFields([
            'url' => $url,
            'user_id' => $userId
        ]);
    }

    /**
     * Update stream status.
     *
     * @param int $id
     * @param string $status
     * @param array $metadata
     * @return bool|array
     */
    public function updateStatus(int $id, string $status, array $metadata = [])
    {
        return $this->executeWithErrorHandling(
            function () use ($id, $status, $metadata) {
                $stream = $this->model->findOrFail($id);

                $data = [
                    'status' => $status,
                    'last_checked_at' => now(),
                ];

                if (!empty($metadata)) {
                    $data['metadata'] = array_merge($stream->metadata ?? [], $metadata);
                }

                return $stream->update($data);
            },
            'updateStatus',
            [
                'id' => $id,
                'status' => $status,
                'metadata_count' => count($metadata),
            ]
        );
    }

    /**
     * Find a stream by id, including soft-deleted streams.
     *
     * @param int $id
     * @param array $columns
     * @return mixed
     */
    public function findWithTrashed(int $id, array $columns = ['*'])
    {
        return $this->model->withTrashed()->find($id, $columns);
    }

    /**
     * Get streams by statuses for a user.
     *
     * @param int $userId
     * @param array $statuses
     * @param array $excludedStreamIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByStatusesForUser(int $userId, array $statuses, array $excludedStreamIds = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->model->newQuery();

        // Apply filters
        $filters = ['user_id' => $userId];
        $query = $this->applyFilters($query, $filters);

        // Apply status filter (using whereIn)
        $query->whereIn('status', $statuses);

        // Exclude specific stream IDs if provided
        if (!empty($excludedStreamIds)) {
            $query->whereNotIn('id', $excludedStreamIds);
        }

        // Apply sorting
        $query = $this->applySorting($query, 'name', 'asc');

        return $query->get();
    }

    /**
     * Get paginated streams with caching and optimization
     *
     * @param int $userId
     * @param array $filters
     * @param int $perPage
     * @param array $columns
     * @return LengthAwarePaginator
     */
    public function getPaginatedOptimized(
        int $userId,
        array $filters = [],
        int $perPage = 15,
        array $columns = ['*']
    ): LengthAwarePaginator {
        $cacheKey = 'stream:paginated:' . $userId . ':' . md5(serialize($filters));

        $query = $this->buildOptimizedQuery($userId, $filters, $columns);

        return $this->getCachedPaginated($query, $perPage, $cacheKey, CacheService::TTL_SHORT);
    }

    /**
     * Build optimized query for streams
     *
     * @param int $userId
     * @param array $filters
     * @param array $columns
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function buildOptimizedQuery(int $userId, array $filters = [], array $columns = ['*'])
    {
        $query = $this->model->newQuery();

        // Apply selective columns for better performance
        $query = $this->applySelectiveColumns($query, $columns);

        // Always filter by user
        $query->where('user_id', $userId);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['search']) && !empty($filters['search'])) {
            $searchFields = ['name', 'url', 'tvg_name', 'group_title'];
            $query = $this->applyOptimizedSearch($query, $filters['search'], $searchFields);
        }

        if (isset($filters['group_title'])) {
            $query->where('group_title', $filters['group_title']);
        }

        if (isset($filters['exclude_from_export'])) {
            $query->where('exclude_from_export', $filters['exclude_from_export']);
        }

        // Apply sorting
        $sortBy = $filters['sort'] ?? 'name';
        $sortDirection = $filters['direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        return $query;
    }

    /**
     * Get user statistics with caching
     *
     * @param int $userId
     * @return array
     */
    public function getUserStats(int $userId): array
    {
        $cacheKey = 'stream:stats:' . $userId;

        return $this->getCacheService()->remember(
            $cacheKey,
            function () use ($userId) {
                return [
                    'total' => $this->model->where('user_id', $userId)->count(),
                    'online' => $this->model->where('user_id', $userId)->where('status', 'online')->count(),
                    'offline' => $this->model->where('user_id', $userId)->where('status', 'offline')->count(),
                    'unknown' => $this->model->where('user_id', $userId)->where('status', 'unknown')->count(),
                    'by_group' => $this->getGroupStats($userId),
                ];
            },
            CacheService::TTL_MEDIUM
        );
    }

    /**
     * Get group statistics
     *
     * @param int $userId
     * @return array
     */
    protected function getGroupStats(int $userId): array
    {
        return $this->model
            ->where('user_id', $userId)
            ->whereNotNull('group_title')
            ->selectRaw('group_title, COUNT(*) as count')
            ->groupBy('group_title')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->pluck('count', 'group_title')
            ->toArray();
    }

    /**
     * Invalidate user-related caches
     *
     * @param int $userId
     * @return void
     */
    public function invalidateUserCaches(int $userId): void
    {
        $patterns = [
            'stream:paginated:' . $userId . '*',
            'stream:stats:' . $userId,
            'stream:search:*',
        ];

        $this->invalidateRelatedCaches($patterns);
    }

    /**
     * Bulk update streams with optimization
     *
     * @param array $streamIds
     * @param array $data
     * @return int
     */
    public function bulkUpdate(array $streamIds, array $data): int
    {
        if (empty($streamIds)) {
            return 0;
        }

        // Process in batches to avoid memory issues
        $batchSize = 1000;
        $updated = 0;

        foreach (array_chunk($streamIds, $batchSize) as $batch) {
            $updated += $this->model->whereIn('id', $batch)->update($data);
        }

        // Invalidate related caches
        foreach ($streamIds as $streamId) {
            $this->getCacheService()->invalidateStream($streamId);
        }

        return $updated;
    }
}
