<?php

namespace App\Services;

use App\Models\Category;
use App\Repositories\CategoryRepository;
use Illuminate\Support\Str;

class CategoryService
{
    /**
     * @var CategoryRepository
     */
    protected $categoryRepository;

    /**
     * CategoryService constructor.
     *
     * @param CategoryRepository $categoryRepository
     */
    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * Create a new category.
     *
     * @param array $data
     * @return Category
     */
    public function createCategory(array $data): Category
    {
        // Generate slug if not provided
        if (!isset($data['slug']) || empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        return $this->categoryRepository->create($data);
    }

    /**
     * Update a category.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateCategory(int $id, array $data): bool
    {
        // Generate slug if not provided and name has changed
        $category = $this->categoryRepository->find($id);
        
        if (isset($data['name']) && $category->name !== $data['name'] && (!isset($data['slug']) || empty($data['slug']))) {
            $data['slug'] = Str::slug($data['name']);
        }

        return $this->categoryRepository->update($data, $id);
    }

    /**
     * Get root categories.
     *
     * @param int|null $userId
     * @return array
     */
    public function getRootCategories(?int $userId = null): array
    {
        return $this->categoryRepository->getRootCategories($userId)->toArray();
    }

    /**
     * Get child categories.
     *
     * @param int $parentId
     * @return array
     */
    public function getChildCategories(int $parentId): array
    {
        return $this->categoryRepository->getChildren($parentId)->toArray();
    }

    /**
     * Add stream to category.
     *
     * @param int $categoryId
     * @param int $streamId
     * @return bool
     */
    public function addStreamToCategory(int $categoryId, int $streamId): bool
    {
        return $this->categoryRepository->addStream($categoryId, $streamId);
    }

    /**
     * Remove stream from category.
     *
     * @param int $categoryId
     * @param int $streamId
     * @return bool
     */
    public function removeStreamFromCategory(int $categoryId, int $streamId): bool
    {
        return $this->categoryRepository->removeStream($categoryId, $streamId);
    }

    /**
     * Get category by slug.
     *
     * @param string $slug
     * @return Category|null
     */
    public function getCategoryBySlug(string $slug): ?Category
    {
        return $this->categoryRepository->findBySlug($slug);
    }
}
