<?php

namespace App\Services;

use App\Models\Tag;
use App\Repositories\TagRepository;
use Illuminate\Support\Str;

class TagService
{
    /**
     * @var TagRepository
     */
    protected $tagRepository;

    /**
     * TagService constructor.
     *
     * @param TagRepository $tagRepository
     */
    public function __construct(TagRepository $tagRepository)
    {
        $this->tagRepository = $tagRepository;
    }

    /**
     * Create a new tag.
     *
     * @param array $data
     * @return Tag
     */
    public function createTag(array $data): Tag
    {
        // Generate slug if not provided
        if (!isset($data['slug']) || empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        return $this->tagRepository->create($data);
    }

    /**
     * Update a tag.
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateTag(int $id, array $data): bool
    {
        // Generate slug if not provided and name has changed
        $tag = $this->tagRepository->find($id);
        
        if (isset($data['name']) && $tag->name !== $data['name'] && (!isset($data['slug']) || empty($data['slug']))) {
            $data['slug'] = Str::slug($data['name']);
        }

        return $this->tagRepository->update($data, $id);
    }

    /**
     * Find or create a tag by name.
     *
     * @param string $name
     * @param int $userId
     * @return Tag
     */
    public function findOrCreateByName(string $name, int $userId): Tag
    {
        return $this->tagRepository->findOrCreateByName($name, $userId);
    }

    /**
     * Get popular tags.
     *
     * @param int $limit
     * @return array
     */
    public function getPopularTags(int $limit = 10): array
    {
        return $this->tagRepository->getPopular($limit)->toArray();
    }

    /**
     * Add stream to tag.
     *
     * @param int $tagId
     * @param int $streamId
     * @return bool
     */
    public function addStreamToTag(int $tagId, int $streamId): bool
    {
        return $this->tagRepository->addStream($tagId, $streamId);
    }

    /**
     * Remove stream from tag.
     *
     * @param int $tagId
     * @param int $streamId
     * @return bool
     */
    public function removeStreamFromTag(int $tagId, int $streamId): bool
    {
        return $this->tagRepository->removeStream($tagId, $streamId);
    }

    /**
     * Get tag by slug.
     *
     * @param string $slug
     * @return Tag|null
     */
    public function getTagBySlug(string $slug): ?Tag
    {
        return $this->tagRepository->findBySlug($slug);
    }
}
