<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

/**
 * Centralized Cache Service
 * 
 * Provides a unified interface for caching operations with
 * intelligent cache key management and TTL strategies.
 */
class CacheService
{
    // Cache TTL constants (in seconds)
    const TTL_SHORT = 300;      // 5 minutes
    const TTL_MEDIUM = 1800;    // 30 minutes
    const TTL_LONG = 3600;      // 1 hour
    const TTL_VERY_LONG = 86400; // 24 hours

    // Cache key prefixes
    const PREFIX_STREAM = 'stream:';
    const PREFIX_USER = 'user:';
    const PREFIX_PLAYLIST = 'playlist:';
    const PREFIX_CATEGORY = 'category:';
    const PREFIX_TAG = 'tag:';
    const PREFIX_DASHBOARD = 'dashboard:';
    const PREFIX_EXPORT = 'export:';
    const PREFIX_VALIDATION = 'validation:';

    /**
     * Get cached data or execute callback and cache result
     *
     * @param string $key
     * @param callable $callback
     * @param int $ttl
     * @return mixed
     */
    public function remember(string $key, callable $callback, int $ttl = self::TTL_MEDIUM)
    {
        try {
            return Cache::remember($key, $ttl, $callback);
        } catch (\Exception $e) {
            Log::warning('Cache remember failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            // Fallback to direct execution if cache fails
            return $callback();
        }
    }

    /**
     * Cache stream data
     *
     * @param int $streamId
     * @param string $subKey
     * @param mixed $data
     * @param int $ttl
     * @return bool
     */
    public function cacheStream(int $streamId, string $subKey, $data, int $ttl = self::TTL_MEDIUM): bool
    {
        $key = self::PREFIX_STREAM . $streamId . ':' . $subKey;
        return $this->put($key, $data, $ttl);
    }

    /**
     * Get cached stream data
     *
     * @param int $streamId
     * @param string $subKey
     * @return mixed
     */
    public function getStream(int $streamId, string $subKey)
    {
        $key = self::PREFIX_STREAM . $streamId . ':' . $subKey;
        return $this->get($key);
    }

    /**
     * Cache user dashboard data
     *
     * @param int $userId
     * @param mixed $data
     * @param int $ttl
     * @return bool
     */
    public function cacheDashboard(int $userId, $data, int $ttl = self::TTL_SHORT): bool
    {
        $key = self::PREFIX_DASHBOARD . $userId;
        return $this->put($key, $data, $ttl);
    }

    /**
     * Get cached dashboard data
     *
     * @param int $userId
     * @return mixed
     */
    public function getDashboard(int $userId)
    {
        $key = self::PREFIX_DASHBOARD . $userId;
        return $this->get($key);
    }

    /**
     * Cache stream validation result
     *
     * @param int $streamId
     * @param array $result
     * @param int $ttl
     * @return bool
     */
    public function cacheValidation(int $streamId, array $result, int $ttl = self::TTL_LONG): bool
    {
        $key = self::PREFIX_VALIDATION . $streamId;
        return $this->put($key, $result, $ttl);
    }

    /**
     * Get cached validation result
     *
     * @param int $streamId
     * @return mixed
     */
    public function getValidation(int $streamId)
    {
        $key = self::PREFIX_VALIDATION . $streamId;
        return $this->get($key);
    }

    /**
     * Cache user statistics
     *
     * @param int $userId
     * @param array $stats
     * @param int $ttl
     * @return bool
     */
    public function cacheUserStats(int $userId, array $stats, int $ttl = self::TTL_MEDIUM): bool
    {
        $key = self::PREFIX_USER . $userId . ':stats';
        return $this->put($key, $stats, $ttl);
    }

    /**
     * Get cached user statistics
     *
     * @param int $userId
     * @return mixed
     */
    public function getUserStats(int $userId)
    {
        $key = self::PREFIX_USER . $userId . ':stats';
        return $this->get($key);
    }

    /**
     * Cache export job data
     *
     * @param string $jobId
     * @param mixed $data
     * @param int $ttl
     * @return bool
     */
    public function cacheExport(string $jobId, $data, int $ttl = self::TTL_VERY_LONG): bool
    {
        $key = self::PREFIX_EXPORT . $jobId;
        return $this->put($key, $data, $ttl);
    }

    /**
     * Get cached export data
     *
     * @param string $jobId
     * @return mixed
     */
    public function getExport(string $jobId)
    {
        $key = self::PREFIX_EXPORT . $jobId;
        return $this->get($key);
    }

    /**
     * Invalidate cache by pattern
     *
     * @param string $pattern
     * @return int Number of keys deleted
     */
    public function invalidatePattern(string $pattern): int
    {
        try {
            if (config('cache.default') === 'redis') {
                $keys = Redis::keys($pattern);
                if (!empty($keys)) {
                    return Redis::del($keys);
                }
            } else {
                // For non-Redis cache drivers, we can't pattern match
                // This is a limitation of file/database cache drivers
                Log::info('Pattern invalidation not supported for current cache driver');
            }
            
            return 0;
        } catch (\Exception $e) {
            Log::error('Cache pattern invalidation failed', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }

    /**
     * Invalidate all cache for a user
     *
     * @param int $userId
     * @return int
     */
    public function invalidateUser(int $userId): int
    {
        $pattern = self::PREFIX_USER . $userId . '*';
        return $this->invalidatePattern($pattern);
    }

    /**
     * Invalidate all cache for a stream
     *
     * @param int $streamId
     * @return int
     */
    public function invalidateStream(int $streamId): int
    {
        $pattern = self::PREFIX_STREAM . $streamId . '*';
        return $this->invalidatePattern($pattern);
    }

    /**
     * Invalidate dashboard cache for a user
     *
     * @param int $userId
     * @return bool
     */
    public function invalidateDashboard(int $userId): bool
    {
        $key = self::PREFIX_DASHBOARD . $userId;
        return $this->forget($key);
    }

    /**
     * Put data in cache
     *
     * @param string $key
     * @param mixed $data
     * @param int $ttl
     * @return bool
     */
    protected function put(string $key, $data, int $ttl): bool
    {
        try {
            return Cache::put($key, $data, $ttl);
        } catch (\Exception $e) {
            Log::warning('Cache put failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get data from cache
     *
     * @param string $key
     * @return mixed
     */
    protected function get(string $key)
    {
        try {
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::warning('Cache get failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Remove data from cache
     *
     * @param string $key
     * @return bool
     */
    protected function forget(string $key): bool
    {
        try {
            return Cache::forget($key);
        } catch (\Exception $e) {
            Log::warning('Cache forget failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Get cache statistics
     *
     * @return array
     */
    public function getStats(): array
    {
        try {
            if (config('cache.default') === 'redis') {
                $info = Redis::info();
                return [
                    'driver' => 'redis',
                    'memory_usage' => $info['used_memory_human'] ?? 'N/A',
                    'connected_clients' => $info['connected_clients'] ?? 'N/A',
                    'total_commands_processed' => $info['total_commands_processed'] ?? 'N/A',
                    'keyspace_hits' => $info['keyspace_hits'] ?? 'N/A',
                    'keyspace_misses' => $info['keyspace_misses'] ?? 'N/A',
                ];
            }
            
            return [
                'driver' => config('cache.default'),
                'status' => 'active'
            ];
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
}
