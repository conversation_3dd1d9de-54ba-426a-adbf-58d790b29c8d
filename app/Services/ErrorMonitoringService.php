<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * Error Monitoring Service
 * 
 * Provides comprehensive error tracking, reporting, and analysis
 * for improved application reliability and debugging.
 */
class ErrorMonitoringService
{
    // Error severity levels
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    // Error categories
    const CATEGORY_DATABASE = 'database';
    const CATEGORY_CACHE = 'cache';
    const CATEGORY_EXTERNAL_API = 'external_api';
    const CATEGORY_VALIDATION = 'validation';
    const CATEGORY_AUTHENTICATION = 'authentication';
    const CATEGORY_STREAM_PROCESSING = 'stream_processing';
    const CATEGORY_FILE_SYSTEM = 'file_system';
    const CATEGORY_GENERAL = 'general';

    /**
     * Report an error with context
     *
     * @param Throwable $exception
     * @param array $context
     * @param string $severity
     * @param string $category
     * @return void
     */
    public function reportError(
        Throwable $exception,
        array $context = [],
        string $severity = self::SEVERITY_MEDIUM,
        string $category = self::CATEGORY_GENERAL
    ): void {
        $errorData = $this->buildErrorData($exception, $context, $severity, $category);
        
        // Log the error
        $this->logError($errorData);
        
        // Store for analysis
        $this->storeError($errorData);
        
        // Update error statistics
        $this->updateErrorStats($severity, $category);
        
        // Check if we need to trigger alerts
        $this->checkAlertThresholds($severity, $category);
    }

    /**
     * Report a custom error
     *
     * @param string $message
     * @param array $context
     * @param string $severity
     * @param string $category
     * @return void
     */
    public function reportCustomError(
        string $message,
        array $context = [],
        string $severity = self::SEVERITY_MEDIUM,
        string $category = self::CATEGORY_GENERAL
    ): void {
        $errorData = [
            'id' => uniqid('error_', true),
            'message' => $message,
            'severity' => $severity,
            'category' => $category,
            'context' => $context,
            'timestamp' => now()->toIso8601String(),
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
        ];
        
        $this->logError($errorData);
        $this->storeError($errorData);
        $this->updateErrorStats($severity, $category);
    }

    /**
     * Get error statistics
     *
     * @param string $period
     * @return array
     */
    public function getErrorStats(string $period = '24h'): array
    {
        $cacheKey = 'error_stats:' . $period;
        
        return Cache::remember($cacheKey, 300, function () use ($period) {
            $timeframe = $this->getTimeframe($period);
            
            return [
                'total_errors' => $this->getErrorCount($timeframe),
                'by_severity' => $this->getErrorsBySeverity($timeframe),
                'by_category' => $this->getErrorsByCategory($timeframe),
                'error_rate' => $this->getErrorRate($timeframe),
                'top_errors' => $this->getTopErrors($timeframe),
                'recent_errors' => $this->getRecentErrors(10),
            ];
        });
    }

    /**
     * Get error trends
     *
     * @param int $days
     * @return array
     */
    public function getErrorTrends(int $days = 7): array
    {
        $trends = [];
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $trends[$date] = $this->getErrorCountForDate($date);
        }
        
        return $trends;
    }

    /**
     * Get error details by ID
     *
     * @param string $errorId
     * @return array|null
     */
    public function getErrorDetails(string $errorId): ?array
    {
        $cacheKey = 'error_details:' . $errorId;
        return Cache::get($cacheKey);
    }

    /**
     * Mark error as resolved
     *
     * @param string $errorId
     * @param string $resolution
     * @return bool
     */
    public function resolveError(string $errorId, string $resolution = ''): bool
    {
        $errorData = $this->getErrorDetails($errorId);
        
        if (!$errorData) {
            return false;
        }
        
        $errorData['resolved'] = true;
        $errorData['resolved_at'] = now()->toIso8601String();
        $errorData['resolution'] = $resolution;
        
        $cacheKey = 'error_details:' . $errorId;
        Cache::put($cacheKey, $errorData, 86400 * 7); // Keep for 7 days
        
        return true;
    }

    /**
     * Get health check status
     *
     * @return array
     */
    public function getHealthStatus(): array
    {
        $stats = $this->getErrorStats('1h');
        $criticalErrors = $stats['by_severity'][self::SEVERITY_CRITICAL] ?? 0;
        $highErrors = $stats['by_severity'][self::SEVERITY_HIGH] ?? 0;
        $errorRate = $stats['error_rate'];
        
        $status = 'healthy';
        $issues = [];
        
        if ($criticalErrors > 0) {
            $status = 'critical';
            $issues[] = "Critical errors detected: {$criticalErrors}";
        } elseif ($highErrors > 5) {
            $status = 'degraded';
            $issues[] = "High number of high-severity errors: {$highErrors}";
        } elseif ($errorRate > 10) {
            $status = 'degraded';
            $issues[] = "High error rate: {$errorRate}%";
        }
        
        return [
            'status' => $status,
            'issues' => $issues,
            'last_check' => now()->toIso8601String(),
            'stats' => $stats
        ];
    }

    /**
     * Build error data structure
     *
     * @param Throwable $exception
     * @param array $context
     * @param string $severity
     * @param string $category
     * @return array
     */
    protected function buildErrorData(
        Throwable $exception,
        array $context,
        string $severity,
        string $category
    ): array {
        return [
            'id' => uniqid('error_', true),
            'message' => $exception->getMessage(),
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'severity' => $severity,
            'category' => $category,
            'context' => $context,
            'timestamp' => now()->toIso8601String(),
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'resolved' => false,
        ];
    }

    /**
     * Log error data
     *
     * @param array $errorData
     * @return void
     */
    protected function logError(array $errorData): void
    {
        $logLevel = $this->getLogLevel($errorData['severity']);
        
        Log::log($logLevel, 'Application Error', [
            'error_id' => $errorData['id'],
            'message' => $errorData['message'],
            'severity' => $errorData['severity'],
            'category' => $errorData['category'],
            'context' => $errorData['context'],
        ]);
    }

    /**
     * Store error for analysis
     *
     * @param array $errorData
     * @return void
     */
    protected function storeError(array $errorData): void
    {
        // Store individual error details
        $cacheKey = 'error_details:' . $errorData['id'];
        Cache::put($cacheKey, $errorData, 86400 * 7); // Keep for 7 days
        
        // Add to recent errors list
        $recentKey = 'recent_errors';
        $recent = Cache::get($recentKey, []);
        array_unshift($recent, $errorData['id']);
        
        // Keep only last 100 errors
        if (count($recent) > 100) {
            $recent = array_slice($recent, 0, 100);
        }
        
        Cache::put($recentKey, $recent, 86400);
    }

    /**
     * Update error statistics
     *
     * @param string $severity
     * @param string $category
     * @return void
     */
    protected function updateErrorStats(string $severity, string $category): void
    {
        $hour = now()->format('Y-m-d-H');
        $day = now()->format('Y-m-d');
        
        // Update hourly stats
        $this->incrementCounter("error_count:hour:{$hour}");
        $this->incrementCounter("error_severity:{$severity}:hour:{$hour}");
        $this->incrementCounter("error_category:{$category}:hour:{$hour}");
        
        // Update daily stats
        $this->incrementCounter("error_count:day:{$day}");
        $this->incrementCounter("error_severity:{$severity}:day:{$day}");
        $this->incrementCounter("error_category:{$category}:day:{$day}");
    }

    /**
     * Increment counter in cache
     *
     * @param string $key
     * @return void
     */
    protected function incrementCounter(string $key): void
    {
        $current = Cache::get($key, 0);
        Cache::put($key, $current + 1, 86400 * 7); // Keep for 7 days
    }

    /**
     * Check alert thresholds
     *
     * @param string $severity
     * @param string $category
     * @return void
     */
    protected function checkAlertThresholds(string $severity, string $category): void
    {
        $hour = now()->format('Y-m-d-H');
        
        // Check critical error threshold
        if ($severity === self::SEVERITY_CRITICAL) {
            $this->triggerAlert('critical_error', [
                'severity' => $severity,
                'category' => $category,
                'hour' => $hour
            ]);
        }
        
        // Check high error rate threshold
        $hourlyCount = Cache::get("error_count:hour:{$hour}", 0);
        if ($hourlyCount > 50) { // More than 50 errors per hour
            $this->triggerAlert('high_error_rate', [
                'count' => $hourlyCount,
                'hour' => $hour
            ]);
        }
    }

    /**
     * Trigger alert
     *
     * @param string $type
     * @param array $data
     * @return void
     */
    protected function triggerAlert(string $type, array $data): void
    {
        // Log alert
        Log::critical('Error Alert Triggered', [
            'alert_type' => $type,
            'data' => $data
        ]);
        
        // Here you could integrate with external alerting services
        // like Slack, email notifications, PagerDuty, etc.
    }

    /**
     * Get log level for severity
     *
     * @param string $severity
     * @return string
     */
    protected function getLogLevel(string $severity): string
    {
        return match ($severity) {
            self::SEVERITY_CRITICAL => 'critical',
            self::SEVERITY_HIGH => 'error',
            self::SEVERITY_MEDIUM => 'warning',
            self::SEVERITY_LOW => 'info',
            default => 'error',
        };
    }

    /**
     * Get timeframe for period
     *
     * @param string $period
     * @return \Carbon\Carbon
     */
    protected function getTimeframe(string $period): \Carbon\Carbon
    {
        return match ($period) {
            '1h' => now()->subHour(),
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subDay(),
        };
    }

    /**
     * Get error count for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return int
     */
    protected function getErrorCount(\Carbon\Carbon $since): int
    {
        // Implementation would depend on your storage method
        // This is a simplified version using cache counters
        $count = 0;
        $current = now();
        
        while ($current->greaterThan($since)) {
            $hour = $current->format('Y-m-d-H');
            $count += Cache::get("error_count:hour:{$hour}", 0);
            $current->subHour();
        }
        
        return $count;
    }

    /**
     * Get errors by severity for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return array
     */
    protected function getErrorsBySeverity(\Carbon\Carbon $since): array
    {
        $severities = [
            self::SEVERITY_CRITICAL,
            self::SEVERITY_HIGH,
            self::SEVERITY_MEDIUM,
            self::SEVERITY_LOW
        ];
        
        $result = [];
        
        foreach ($severities as $severity) {
            $count = 0;
            $current = now();
            
            while ($current->greaterThan($since)) {
                $hour = $current->format('Y-m-d-H');
                $count += Cache::get("error_severity:{$severity}:hour:{$hour}", 0);
                $current->subHour();
            }
            
            $result[$severity] = $count;
        }
        
        return $result;
    }

    /**
     * Get errors by category for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return array
     */
    protected function getErrorsByCategory(\Carbon\Carbon $since): array
    {
        $categories = [
            self::CATEGORY_DATABASE,
            self::CATEGORY_CACHE,
            self::CATEGORY_EXTERNAL_API,
            self::CATEGORY_VALIDATION,
            self::CATEGORY_AUTHENTICATION,
            self::CATEGORY_STREAM_PROCESSING,
            self::CATEGORY_FILE_SYSTEM,
            self::CATEGORY_GENERAL
        ];
        
        $result = [];
        
        foreach ($categories as $category) {
            $count = 0;
            $current = now();
            
            while ($current->greaterThan($since)) {
                $hour = $current->format('Y-m-d-H');
                $count += Cache::get("error_category:{$category}:hour:{$hour}", 0);
                $current->subHour();
            }
            
            $result[$category] = $count;
        }
        
        return $result;
    }

    /**
     * Get error rate for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return float
     */
    protected function getErrorRate(\Carbon\Carbon $since): float
    {
        $errorCount = $this->getErrorCount($since);
        $totalRequests = $this->getTotalRequests($since);
        
        if ($totalRequests === 0) {
            return 0;
        }
        
        return round(($errorCount / $totalRequests) * 100, 2);
    }

    /**
     * Get total requests for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return int
     */
    protected function getTotalRequests(\Carbon\Carbon $since): int
    {
        // This would need to be implemented based on your request tracking
        // For now, return a reasonable estimate
        $hours = $since->diffInHours(now());
        return $hours * 100; // Assume 100 requests per hour average
    }

    /**
     * Get top errors for timeframe
     *
     * @param \Carbon\Carbon $since
     * @return array
     */
    protected function getTopErrors(\Carbon\Carbon $since): array
    {
        // This would need more sophisticated tracking
        // For now, return empty array
        return [];
    }

    /**
     * Get recent errors
     *
     * @param int $limit
     * @return array
     */
    protected function getRecentErrors(int $limit = 10): array
    {
        $recentIds = Cache::get('recent_errors', []);
        $errors = [];
        
        foreach (array_slice($recentIds, 0, $limit) as $errorId) {
            $errorData = $this->getErrorDetails($errorId);
            if ($errorData) {
                $errors[] = $errorData;
            }
        }
        
        return $errors;
    }

    /**
     * Get error count for specific date
     *
     * @param string $date
     * @return int
     */
    protected function getErrorCountForDate(string $date): int
    {
        return Cache::get("error_count:day:{$date}", 0);
    }
}
