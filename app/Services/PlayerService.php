<?php

namespace App\Services;

use App\Models\Stream;
use App\Repositories\StreamRepository;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class PlayerService
{
    /**
     * @var StreamRepository
     */
    protected $streamRepository;

    /**
     * PlayerService constructor.
     *
     * @param StreamRepository $streamRepository
     */
    public function __construct(StreamRepository $streamRepository)
    {
        $this->streamRepository = $streamRepository;
    }

    /**
     * Get stream details for playback.
     *
     * @param int $streamId
     * @return array
     */
    public function getStreamForPlayback(int $streamId): array
    {
        $stream = $this->streamRepository->find($streamId);

        // Check if stream is online
        if ($stream->status !== 'online') {
            // Try to validate the stream
            $this->validateStreamForPlayback($streamId);

            // Refresh the stream data
            $stream = $this->streamRepository->find($streamId);
        }

        // Prepare stream data for player
        return [
            'id' => $stream->id,
            'name' => $stream->name,
            'url' => $stream->url,
            'type' => $stream->type,
            'status' => $stream->status,
            'metadata' => $stream->metadata,
            'tvg_logo' => $stream->tvg_logo,
            'proxy_url' => $this->shouldUseProxy($stream) ? $this->getProxyUrl($stream) : null,
        ];
    }

    /**
     * Validate a stream for playback.
     *
     * @param int $streamId
     * @return bool
     */
    public function validateStreamForPlayback(int $streamId): bool
    {
        $stream = $this->streamRepository->find($streamId);

        try {
            // Basic validation - check if the URL is accessible
            $response = Http::timeout(5)->head($stream->url);

            if ($response->successful()) {
                // Stream is online
                $metadata = [
                    'http_code' => $response->status(),
                    'content_type' => $response->header('Content-Type'),
                    'content_length' => $response->header('Content-Length'),
                    'last_validated' => now()->toIso8601String(),
                ];

                return $this->streamRepository->updateStatus($streamId, 'online', $metadata);
            } else {
                // Stream is offline
                $metadata = [
                    'http_code' => $response->status(),
                    'error' => 'HTTP error: ' . $response->status(),
                    'last_validated' => now()->toIso8601String(),
                ];

                return $this->streamRepository->updateStatus($streamId, 'offline', $metadata);
            }
        } catch (\Exception $e) {
            // Error occurred during validation
            $metadata = [
                'error' => $e->getMessage(),
                'last_validated' => now()->toIso8601String(),
            ];

            Log::error('Stream validation error: ' . $e->getMessage(), [
                'stream_id' => $streamId,
                'url' => $stream->url,
            ]);

            return $this->streamRepository->updateStatus($streamId, 'error', $metadata);
        }
    }

    /**
     * Determine if a stream should use a proxy for playback.
     *
     * @param Stream $stream
     * @return bool
     */
    protected function shouldUseProxy(Stream $stream): bool
    {
        // Check if proxy is enabled in config
        if (!Config::get('proxy.enabled', true)) {
            return false;
        }

        // Check if the stream has CORS issues or requires proxy based on metadata
        if (isset($stream->metadata['requires_proxy']) && $stream->metadata['requires_proxy']) {
            return true;
        }

        // Check if the stream has had CORS issues in the past
        if (isset($stream->metadata['cors_issues']) && $stream->metadata['cors_issues']) {
            return true;
        }

        // Check if the stream is from a known domain that requires proxy
        $proxyDomains = Config::get('proxy.proxy_domains', []);
        $streamDomain = parse_url($stream->url, PHP_URL_HOST);

        if (in_array($streamDomain, $proxyDomains)) {
            return true;
        }

        // We no longer automatically proxy HLS streams to avoid server overload
        // Only proxy if there are known CORS issues

        return false;
    }

    /**
     * Get proxy URL for a stream.
     *
     * @param Stream $stream
     * @return string
     */
    protected function getProxyUrl(Stream $stream): string
    {
        // Get expiration time from config
        $expirationHours = Config::get('proxy.url_expiration', 6);

        // Generate a signed URL for the proxy endpoint
        return URL::temporarySignedRoute(
            'player.proxy',
            now()->addHours($expirationHours),
            ['stream' => $stream->id]
        );
    }

    /**
     * Mark a stream as having CORS issues.
     *
     * @param int $streamId
     * @return bool
     */
    public function markStreamWithCorsIssues(int $streamId): bool
    {
        $stream = $this->streamRepository->find($streamId);

        // Update metadata to indicate CORS issues
        $metadata = $stream->metadata ?? [];
        $metadata['cors_issues'] = true;
        $metadata['requires_proxy'] = true;
        $metadata['cors_issues_reported_at'] = now()->toIso8601String();

        // Extract domain for future reference
        $domain = parse_url($stream->url, PHP_URL_HOST);
        if ($domain) {
            $metadata['cors_issues_domain'] = $domain;

            // Add to known CORS domains in config if possible
            $proxyDomains = Config::get('proxy.proxy_domains', []);
            if (!in_array($domain, $proxyDomains)) {
                Log::info("New domain with CORS issues detected: {$domain}");
            }
        }

        // Update the stream
        return $this->streamRepository->update([
            'metadata' => $metadata,
        ], $streamId);
    }

    /**
     * Proxy a stream through the application.
     *
     * @param int $streamId
     * @param string|null $segmentPath Optional path for HLS segment
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function proxyStream(int $streamId, ?string $segmentPath = null)
    {
        $stream = $this->streamRepository->find($streamId);

        // Determine the URL to proxy
        $url = $stream->url;

        // If a segment path is provided, resolve it relative to the stream URL
        if ($segmentPath) {
            $url = $this->resolveSegmentUrl($url, $segmentPath);
        }

        // Generate a cache key for this request
        $cacheKey = 'proxy_stream_' . md5($url);

        // Check if caching is enabled
        $cacheEnabled = Config::get('proxy.cache_enabled', true);
        $cacheTtl = Config::get('proxy.cache_ttl', 300);

        // For HLS manifests, we don't want to cache for too long
        if ($stream->type === 'hls' && !$segmentPath) {
            $cacheTtl = min($cacheTtl, 60); // Max 1 minute for manifests
        }

        // Return cached response if available
        if ($cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Set timeout from config
            $timeout = Config::get('proxy.timeout', 60);

            // Set user agent from config
            $userAgent = Config::get('proxy.user_agent', 'Mozilla/5.0');

            // Get the stream content
            $response = Http::timeout($timeout)
                ->withHeaders([
                    'User-Agent' => $userAgent,
                    'Referer' => url('/'),
                ])
                ->get($url);

            if ($response->successful()) {
                $contentType = $response->header('Content-Type');
                $content = $response->body();

                // For HLS playlists, we need to rewrite URLs
                if ($stream->type === 'hls' &&
                    !$segmentPath &&
                    Config::get('proxy.rewrite_hls', true) &&
                    (Str::contains($contentType, 'application/vnd.apple.mpegurl') ||
                     Str::contains($contentType, 'application/x-mpegurl') ||
                     Str::contains($contentType, 'audio/mpegurl') ||
                     Str::contains($contentType, 'audio/x-mpegurl'))) {

                    $content = $this->rewriteHlsPlaylist($content, $streamId, $url);
                }

                // Create the response
                $proxyResponse = response($content)
                    ->header('Content-Type', $contentType)
                    ->header('Access-Control-Allow-Origin', '*')
                    ->header('Access-Control-Allow-Methods', 'GET, OPTIONS')
                    ->header('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization, X-Request-With')
                    ->header('Access-Control-Allow-Credentials', 'true');

                // Cache the response if caching is enabled
                if ($cacheEnabled) {
                    Cache::put($cacheKey, $proxyResponse, $cacheTtl);
                }

                return $proxyResponse;
            } else {
                // Stream is offline or error
                Log::error('Stream proxy error: HTTP ' . $response->status(), [
                    'stream_id' => $streamId,
                    'url' => $url,
                ]);

                $errorResponse = response()->json([
                    'error' => 'Failed to proxy stream: HTTP ' . $response->status(),
                ], 500);

                return $errorResponse;
            }
        } catch (\Exception $e) {
            Log::error('Stream proxy error: ' . $e->getMessage(), [
                'stream_id' => $streamId,
                'url' => $url,
            ]);

            return response()->json([
                'error' => 'Failed to proxy stream: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Rewrite URLs in an HLS playlist to use the proxy.
     *
     * @param string $content The playlist content
     * @param int $streamId The stream ID
     * @param string $baseUrl The base URL of the playlist
     * @return string The rewritten playlist
     */
    protected function rewriteHlsPlaylist(string $content, int $streamId, string $baseUrl): string
    {
        $lines = explode("\n", $content);
        $rewrittenLines = [];

        foreach ($lines as $line) {
            $trimmedLine = trim($line);

            // Skip empty lines and comments
            if (empty($trimmedLine) || Str::startsWith($trimmedLine, '#')) {
                $rewrittenLines[] = $line;
                continue;
            }

            // This is a URL - rewrite it to use the proxy
            $segmentUrl = $this->resolveSegmentUrl($baseUrl, $trimmedLine);
            $segmentPath = urlencode($trimmedLine);

            // Get the filename part of the segment
            $filename = basename($segmentUrl);

            // Use direct URL format for better compatibility with HLS.js
            $directProxyUrl = url("/player/{$streamId}/proxy/{$filename}");
            $rewrittenLines[] = $directProxyUrl;
        }

        return implode("\n", $rewrittenLines);
    }

    /**
     * Resolve a segment URL relative to a base URL.
     *
     * @param string $baseUrl The base URL
     * @param string $segmentPath The segment path
     * @return string The resolved URL
     */
    protected function resolveSegmentUrl(string $baseUrl, string $segmentPath): string
    {
        // If the segment path is already an absolute URL, return it
        if (Str::startsWith($segmentPath, ['http://', 'https://'])) {
            return $segmentPath;
        }

        // Parse the base URL
        $parsedUrl = parse_url($baseUrl);

        // If the segment path starts with a slash, it's relative to the root
        if (Str::startsWith($segmentPath, '/')) {
            $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] : 'http';
            $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
            $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';

            return "{$scheme}://{$host}{$port}{$segmentPath}";
        }

        // Otherwise, it's relative to the directory of the base URL
        $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
        $dir = Str::contains($path, '/') ? Str::beforeLast($path, '/') : '';

        $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] : 'http';
        $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
        $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';

        return "{$scheme}://{$host}{$port}{$dir}/{$segmentPath}";
    }
}
