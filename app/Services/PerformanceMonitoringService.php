<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

/**
 * Performance Monitoring Service
 * 
 * Monitors application performance metrics and provides
 * insights for optimization opportunities.
 */
class PerformanceMonitoringService
{
    /**
     * Track query performance
     *
     * @param string $operation
     * @param callable $callback
     * @return mixed
     */
    public function trackQuery(string $operation, callable $callback)
    {
        $startTime = microtime(true);
        $startQueries = $this->getQueryCount();
        
        try {
            $result = $callback();
            
            $endTime = microtime(true);
            $endQueries = $this->getQueryCount();
            
            $this->logPerformanceMetrics($operation, [
                'execution_time' => round(($endTime - $startTime) * 1000, 2), // ms
                'query_count' => $endQueries - $startQueries,
                'memory_usage' => $this->getMemoryUsage(),
                'status' => 'success'
            ]);
            
            return $result;
        } catch (\Exception $e) {
            $endTime = microtime(true);
            
            $this->logPerformanceMetrics($operation, [
                'execution_time' => round(($endTime - $startTime) * 1000, 2),
                'query_count' => $this->getQueryCount() - $startQueries,
                'memory_usage' => $this->getMemoryUsage(),
                'status' => 'error',
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Track cache performance
     *
     * @param string $operation
     * @param string $key
     * @param bool $hit
     * @param float $executionTime
     * @return void
     */
    public function trackCache(string $operation, string $key, bool $hit, float $executionTime = null): void
    {
        $metrics = [
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit,
            'execution_time' => $executionTime,
            'timestamp' => now()->toIso8601String()
        ];

        $this->logPerformanceMetrics('cache', $metrics);
        
        // Update cache statistics
        $this->updateCacheStats($hit);
    }

    /**
     * Get database performance metrics
     *
     * @return array
     */
    public function getDatabaseMetrics(): array
    {
        try {
            $metrics = [];
            
            // Get slow query log if available (MySQL)
            if (DB::getDriverName() === 'mysql') {
                $slowQueries = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
                $metrics['slow_queries'] = $slowQueries[0]->Value ?? 0;
                
                $connections = DB::select("SHOW GLOBAL STATUS LIKE 'Connections'");
                $metrics['total_connections'] = $connections[0]->Value ?? 0;
                
                $uptime = DB::select("SHOW GLOBAL STATUS LIKE 'Uptime'");
                $metrics['uptime'] = $uptime[0]->Value ?? 0;
            }
            
            // Get current connection count
            $metrics['active_connections'] = DB::select('SELECT COUNT(*) as count FROM information_schema.processlist')[0]->count ?? 0;
            
            return $metrics;
        } catch (\Exception $e) {
            Log::warning('Failed to get database metrics', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get cache performance metrics
     *
     * @return array
     */
    public function getCacheMetrics(): array
    {
        $cacheService = app(CacheService::class);
        $stats = $cacheService->getStats();
        
        // Get cache hit/miss ratios from our tracking
        $hitRate = Cache::get('performance:cache:hit_rate', 0);
        $stats['hit_rate'] = $hitRate;
        
        return $stats;
    }

    /**
     * Get memory usage metrics
     *
     * @return array
     */
    public function getMemoryMetrics(): array
    {
        return [
            'current_usage' => $this->getMemoryUsage(),
            'peak_usage' => $this->getPeakMemoryUsage(),
            'limit' => ini_get('memory_limit'),
            'usage_percentage' => $this->getMemoryUsagePercentage()
        ];
    }

    /**
     * Get application performance summary
     *
     * @return array
     */
    public function getPerformanceSummary(): array
    {
        return [
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'memory' => $this->getMemoryMetrics(),
            'recommendations' => $this->getPerformanceRecommendations()
        ];
    }

    /**
     * Get performance recommendations
     *
     * @return array
     */
    public function getPerformanceRecommendations(): array
    {
        $recommendations = [];
        
        // Check memory usage
        $memoryUsage = $this->getMemoryUsagePercentage();
        if ($memoryUsage > 80) {
            $recommendations[] = [
                'type' => 'memory',
                'severity' => 'high',
                'message' => 'Memory usage is high (' . $memoryUsage . '%). Consider optimizing queries or increasing memory limit.'
            ];
        }
        
        // Check cache hit rate
        $cacheHitRate = Cache::get('performance:cache:hit_rate', 0);
        if ($cacheHitRate < 70) {
            $recommendations[] = [
                'type' => 'cache',
                'severity' => 'medium',
                'message' => 'Cache hit rate is low (' . $cacheHitRate . '%). Consider caching more frequently accessed data.'
            ];
        }
        
        // Check for slow queries (if available)
        $dbMetrics = $this->getDatabaseMetrics();
        if (isset($dbMetrics['slow_queries']) && $dbMetrics['slow_queries'] > 0) {
            $recommendations[] = [
                'type' => 'database',
                'severity' => 'high',
                'message' => 'Slow queries detected (' . $dbMetrics['slow_queries'] . '). Review and optimize database queries.'
            ];
        }
        
        return $recommendations;
    }

    /**
     * Log performance metrics
     *
     * @param string $operation
     * @param array $metrics
     * @return void
     */
    protected function logPerformanceMetrics(string $operation, array $metrics): void
    {
        Log::info('Performance metrics', [
            'operation' => $operation,
            'metrics' => $metrics
        ]);
        
        // Store metrics for analysis (could be sent to external monitoring service)
        $this->storeMetrics($operation, $metrics);
    }

    /**
     * Store metrics for analysis
     *
     * @param string $operation
     * @param array $metrics
     * @return void
     */
    protected function storeMetrics(string $operation, array $metrics): void
    {
        // Store in cache for short-term analysis
        $key = 'performance:metrics:' . $operation . ':' . date('Y-m-d-H');
        $existing = Cache::get($key, []);
        $existing[] = $metrics;
        
        // Keep only last 100 entries per hour
        if (count($existing) > 100) {
            $existing = array_slice($existing, -100);
        }
        
        Cache::put($key, $existing, 3600); // 1 hour
    }

    /**
     * Update cache statistics
     *
     * @param bool $hit
     * @return void
     */
    protected function updateCacheStats(bool $hit): void
    {
        $hits = Cache::get('performance:cache:hits', 0);
        $misses = Cache::get('performance:cache:misses', 0);
        
        if ($hit) {
            $hits++;
            Cache::put('performance:cache:hits', $hits, 86400);
        } else {
            $misses++;
            Cache::put('performance:cache:misses', $misses, 86400);
        }
        
        $total = $hits + $misses;
        $hitRate = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        Cache::put('performance:cache:hit_rate', $hitRate, 86400);
    }

    /**
     * Get current query count
     *
     * @return int
     */
    protected function getQueryCount(): int
    {
        return count(DB::getQueryLog());
    }

    /**
     * Get memory usage in MB
     *
     * @return float
     */
    protected function getMemoryUsage(): float
    {
        return round(memory_get_usage(true) / 1024 / 1024, 2);
    }

    /**
     * Get peak memory usage in MB
     *
     * @return float
     */
    protected function getPeakMemoryUsage(): float
    {
        return round(memory_get_peak_usage(true) / 1024 / 1024, 2);
    }

    /**
     * Get memory usage percentage
     *
     * @return float
     */
    protected function getMemoryUsagePercentage(): float
    {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') {
            return 0; // No limit
        }
        
        $limitBytes = $this->convertToBytes($limit);
        $currentBytes = memory_get_usage(true);
        
        return round(($currentBytes / $limitBytes) * 100, 2);
    }

    /**
     * Convert memory limit string to bytes
     *
     * @param string $limit
     * @return int
     */
    protected function convertToBytes(string $limit): int
    {
        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }
}
