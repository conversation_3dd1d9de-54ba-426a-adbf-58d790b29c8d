<?php

namespace App\Utils;

use App\Services\ErrorMonitoringService;
use Exception;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Standardized error handling utility for services.
 */
class ErrorHandler
{
    /**
     * Error monitoring service
     *
     * @var ErrorMonitoringService
     */
    protected static $monitoringService;
    /**
     * Get error monitoring service instance
     *
     * @return ErrorMonitoringService
     */
    protected static function getMonitoringService(): ErrorMonitoringService
    {
        if (!self::$monitoringService) {
            self::$monitoringService = app(ErrorMonitoringService::class);
        }

        return self::$monitoringService;
    }

    /**
     * Handle an exception and return a standardized error response.
     *
     * @param Exception $exception The exception to handle
     * @param string $context The context where the exception occurred (e.g., 'StreamService.validate')
     * @param array $metadata Additional metadata to include in the error response
     * @param bool $logError Whether to log the error (default: true)
     * @param string $severity Error severity level
     * @param string $category Error category
     * @return array Standardized error response
     */
    public static function handleException(
        Exception $exception,
        string $context,
        array $metadata = [],
        bool $logError = true,
        string $severity = ErrorMonitoringService::SEVERITY_MEDIUM,
        string $category = ErrorMonitoringService::CATEGORY_GENERAL
    ): array {
        // Prepare error metadata
        $errorMetadata = array_merge([
            'error' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'error_trace' => self::formatExceptionTrace($exception),
            'timestamp' => now()->toIso8601String(),
            'context' => $context,
        ], $metadata);

        // Report to monitoring service
        self::getMonitoringService()->reportError($exception, $errorMetadata, $severity, $category);

        // Log the error if requested
        if ($logError) {
            Log::error("Error in {$context}: " . $exception->getMessage(), [
                'exception' => $exception,
                'metadata' => $metadata,
            ]);
        }

        return [
            'success' => false,
            'error' => $exception->getMessage(),
            'context' => $context,
            'metadata' => $errorMetadata,
        ];
    }

    /**
     * Format an exception trace for inclusion in error responses.
     *
     * @param Exception $exception The exception to format
     * @param int $limit Maximum number of trace items to include (default: 10)
     * @return array Formatted exception trace
     */
    public static function formatExceptionTrace(Exception $exception, int $limit = 10): array
    {
        $trace = $exception->getTrace();
        $formattedTrace = [];

        // Limit the trace to the specified number of items
        $trace = array_slice($trace, 0, $limit);

        foreach ($trace as $item) {
            $formattedItem = [
                'file' => $item['file'] ?? 'unknown',
                'line' => $item['line'] ?? 0,
                'function' => $item['function'] ?? 'unknown',
            ];

            if (isset($item['class'])) {
                $formattedItem['class'] = $item['class'];
            }

            $formattedTrace[] = $formattedItem;
        }

        return $formattedTrace;
    }

    /**
     * Create a standardized success response.
     *
     * @param array $data The data to include in the response
     * @param string $message A success message (optional)
     * @return array Standardized success response
     */
    public static function createSuccessResponse(array $data = [], string $message = ''): array
    {
        return [
            'success' => true,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * Create a standardized error response without an exception.
     *
     * @param string $message The error message
     * @param string $context The context where the error occurred
     * @param array $metadata Additional metadata to include in the error response
     * @param bool $logError Whether to log the error (default: true)
     * @param string $severity Error severity level
     * @param string $category Error category
     * @return array Standardized error response
     */
    public static function createErrorResponse(
        string $message,
        string $context,
        array $metadata = [],
        bool $logError = true,
        string $severity = ErrorMonitoringService::SEVERITY_MEDIUM,
        string $category = ErrorMonitoringService::CATEGORY_GENERAL
    ): array {
        // Prepare error metadata
        $errorMetadata = array_merge([
            'error' => $message,
            'timestamp' => now()->toIso8601String(),
            'context' => $context,
        ], $metadata);

        // Report to monitoring service
        self::getMonitoringService()->reportCustomError($message, $errorMetadata, $severity, $category);

        // Log the error if requested
        if ($logError) {
            Log::error("Error in {$context}: " . $message, [
                'metadata' => $metadata,
            ]);
        }

        return [
            'success' => false,
            'error' => $message,
            'context' => $context,
            'metadata' => $errorMetadata,
        ];
    }

    /**
     * Handle database errors with specific categorization
     *
     * @param Exception $exception
     * @param string $context
     * @param array $metadata
     * @return array
     */
    public static function handleDatabaseError(Exception $exception, string $context, array $metadata = []): array
    {
        return self::handleException(
            $exception,
            $context,
            $metadata,
            true,
            ErrorMonitoringService::SEVERITY_HIGH,
            ErrorMonitoringService::CATEGORY_DATABASE
        );
    }

    /**
     * Handle cache errors with specific categorization
     *
     * @param Exception $exception
     * @param string $context
     * @param array $metadata
     * @return array
     */
    public static function handleCacheError(Exception $exception, string $context, array $metadata = []): array
    {
        return self::handleException(
            $exception,
            $context,
            $metadata,
            true,
            ErrorMonitoringService::SEVERITY_MEDIUM,
            ErrorMonitoringService::CATEGORY_CACHE
        );
    }

    /**
     * Handle stream processing errors with specific categorization
     *
     * @param Exception $exception
     * @param string $context
     * @param array $metadata
     * @return array
     */
    public static function handleStreamError(Exception $exception, string $context, array $metadata = []): array
    {
        return self::handleException(
            $exception,
            $context,
            $metadata,
            true,
            ErrorMonitoringService::SEVERITY_HIGH,
            ErrorMonitoringService::CATEGORY_STREAM_PROCESSING
        );
    }

    /**
     * Handle external API errors with specific categorization
     *
     * @param Exception $exception
     * @param string $context
     * @param array $metadata
     * @return array
     */
    public static function handleExternalApiError(Exception $exception, string $context, array $metadata = []): array
    {
        return self::handleException(
            $exception,
            $context,
            $metadata,
            true,
            ErrorMonitoringService::SEVERITY_MEDIUM,
            ErrorMonitoringService::CATEGORY_EXTERNAL_API
        );
    }
}
