<?php

namespace App\Console;

use App\Jobs\CheckStreamStatusJob;
use App\Jobs\ProcessStreamSchedulesJob;
use App\Jobs\ProcessUrlListSchedulesJob;
use App\Jobs\ProcessSchedulerTasksJob;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Check stream status every hour
        $schedule->job(new CheckStreamStatusJob(24))->hourly();

        // Run a more frequent check for important streams (every 15 minutes)
        // This could be implemented later with a priority flag on streams
        // $schedule->job(new CheckStreamStatusJob(1))->everyFifteenMinutes();

        // Clean up failed jobs once a day
        $schedule->command('queue:prune-failed')->daily();

        // Clean up expired exports daily
        $schedule->command('app:prune-exports')->daily();

        // Monitor Horizon and restart if needed every 5 minutes
        $schedule->command('horizon:monitor')->everyFiveMinutes();

        // Take a snapshot of Horizon metrics every 5 minutes
        $schedule->command('horizon:snapshot')->everyFiveMinutes();

        // Process stream schedules every 15 minutes
        $schedule->job(new ProcessStreamSchedulesJob())->everyFifteenMinutes();

        // Process URL list import schedules every 15 minutes
        $schedule->job(new ProcessUrlListSchedulesJob())->everyFifteenMinutes();

        // Update playlist cluster streams every hour
        $schedule->command('app:update-playlist-cluster-streams')->hourly();

        // Process scheduler tasks every 5 minutes
        $schedule->job(new ProcessSchedulerTasksJob())->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
