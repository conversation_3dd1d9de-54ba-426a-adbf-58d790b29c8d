<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class TruncateAllTablesExceptUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate all tables except users table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!app()->environment('local', 'development', 'testing')) {
            $this->error('This command can only be run in local, development, or testing environments.');
            return Command::FAILURE;
        }

        if (!$this->confirm('This will delete ALL data from all tables except users. Are you sure you want to continue?', false)) {
            $this->info('Operation cancelled.');
            return Command::SUCCESS;
        }

        $this->info('Getting all tables...');

        // Get all tables using the Laravel 12 approach
        $tables = [];
        foreach (DB::select('SHOW TABLES') as $table) {
            $tables[] = reset($table);
        }

        // Tables to exclude from truncation
        $excludeTables = [
            'users',
            'migrations',
            'password_reset_tokens',
            'personal_access_tokens',
            'sessions',
        ];

        // Filter out excluded tables
        $tablesToTruncate = array_filter($tables, function ($table) use ($excludeTables) {
            return !in_array($table, $excludeTables);
        });

        $this->info('The following tables will be truncated:');
        foreach ($tablesToTruncate as $table) {
            $this->line("- {$table}");
        }

        if (!$this->confirm('Continue?', false)) {
            $this->info('Operation cancelled.');
            return Command::SUCCESS;
        }

        $this->info('Truncating tables...');

        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Truncate each table
        foreach ($tablesToTruncate as $table) {
            $this->line("Truncating {$table}...");
            DB::table($table)->truncate();
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        $this->info('All tables have been truncated successfully except users table.');

        return Command::SUCCESS;
    }
}
