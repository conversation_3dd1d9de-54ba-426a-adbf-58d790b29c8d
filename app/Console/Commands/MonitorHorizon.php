<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class MonitorHorizon extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'horizon:monitor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor Horizon and restart it if needed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking Horizon status...');
        
        // Check if Horizon is running
        $output = shell_exec('ps aux | grep "horizon" | grep -v grep');
        
        if (empty($output)) {
            $this->warn('Horizon is not running. Starting...');
            Log::warning('Horizon is not running. Starting...');
            
            // Start Horizon
            $this->info('Starting Horizon...');
            
            // Use the exec command to start Horizon in the background
            exec('php artisan horizon > /dev/null 2>&1 &');
            
            $this->info('Horizon started successfully.');
            Log::info('Horizon started successfully.');
        } else {
            // Horizon is running, but we'll check if it needs to be restarted
            $this->info('Horizon is running. Checking if it needs to be restarted...');
            
            // Check if there are any failed jobs
            $failedCount = Artisan::call('horizon:status');
            
            if ($failedCount > 0) {
                $this->warn('Horizon has failed jobs. Restarting...');
                Log::warning('Horizon has failed jobs. Restarting...');
                
                // Terminate Horizon
                Artisan::call('horizon:terminate');
                
                // Wait for Horizon to terminate
                sleep(5);
                
                // Start Horizon again
                exec('php artisan horizon > /dev/null 2>&1 &');
                
                $this->info('Horizon restarted successfully.');
                Log::info('Horizon restarted successfully.');
            } else {
                $this->info('Horizon is running properly.');
            }
        }
        
        return 0;
    }
}
