<?php

namespace App\Jobs;

use App\Services\StreamService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckStreamStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1; // Only try once as this is a scheduled job

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600; // 1 hour for checking many streams

    /**
     * The maximum number of streams to check in one batch.
     *
     * @var int
     */
    protected $batchSize = 100;

    /**
     * The number of hours since last check to consider a stream for checking.
     *
     * @var int
     */
    protected $hoursThreshold;

    /**
     * Create a new job instance.
     *
     * @param int $hoursThreshold
     */
    public function __construct(int $hoursThreshold = 24)
    {
        $this->hoursThreshold = $hoursThreshold;
        $this->onQueue('streams');
    }

    /**
     * Execute the job.
     */
    public function handle(StreamService $streamService): void
    {
        try {
            Log::info('Starting stream status check', [
                'hours_threshold' => $this->hoursThreshold,
            ]);

            // Get streams that need to be checked
            $streamIds = $streamService->getStreamsNeedingCheck($this->hoursThreshold);
            $totalStreams = count($streamIds);

            Log::info('Found streams to check', [
                'count' => $totalStreams,
            ]);

            if ($totalStreams === 0) {
                return;
            }

            // Process in batches to avoid memory issues
            $batches = array_chunk($streamIds, $this->batchSize);

            foreach ($batches as $index => $batchStreamIds) {
                Log::info('Processing batch', [
                    'batch' => $index + 1,
                    'total_batches' => count($batches),
                    'batch_size' => count($batchStreamIds),
                ]);

                // Queue individual validation jobs for each stream in the batch
                foreach ($batchStreamIds as $streamId) {
                    ValidateStreamJob::dispatch($streamId);
                }
            }

            Log::info('Stream status check completed', [
                'total_streams' => $totalStreams,
                'total_batches' => count($batches),
            ]);
        } catch (\Exception $e) {
            Log::error('Stream status check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Log the error but don't throw it to prevent the queue worker from stopping
            // This is a scheduled job, so it will run again at the next scheduled time
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Stream status check job failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
