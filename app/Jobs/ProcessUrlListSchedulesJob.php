<?php

namespace App\Jobs;

use App\Models\UrlListImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessUrlListSchedulesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue('schedules');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting URL list schedule processing');

            // Get all active URL list imports that are due to run
            $urlListImports = UrlListImport::due()->get();
            $count = $urlListImports->count();

            Log::info('Found URL list imports to process', [
                'count' => $count,
            ]);

            if ($count === 0) {
                return;
            }

            foreach ($urlListImports as $urlListImport) {
                // Dispatch a job to process each URL list import
                ProcessUrlListImportJob::dispatch($urlListImport);

                Log::info('URL list import scheduled for processing', [
                    'url_list_import_id' => $urlListImport->id,
                    'url' => $urlListImport->url,
                ]);
            }

            Log::info('URL list schedule processing completed', [
                'total_imports' => $count,
            ]);
        } catch (\Exception $e) {
            Log::error('URL list schedule processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }
}
