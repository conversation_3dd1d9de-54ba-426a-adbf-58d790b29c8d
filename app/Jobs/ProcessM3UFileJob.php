<?php

namespace App\Jobs;

use App\Repositories\M3UImportRepository;
use App\Services\M3UParserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessM3UFileJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes for large files

    /**
     * The file path or URL to process.
     *
     * @var string
     */
    public $source;

    /**
     * The user ID.
     *
     * @var int
     */
    public $userId;

    /**
     * Whether the source is a URL or a local file.
     *
     * @var bool
     */
    public $isUrl;

    /**
     * Create a new job instance.
     *
     * @param string $source File path or URL
     * @param int $userId
     * @param bool $isUrl
     */
    public function __construct(string $source, int $userId, bool $isUrl = false)
    {
        $this->source = $source;
        $this->userId = $userId;
        $this->isUrl = $isUrl;
        $this->onQueue('imports');
    }

    /**
     * Execute the job.
     */
    public function handle(M3UParserService $m3uParserService, M3UImportRepository $m3uImportRepository): void
    {
        try {
            Log::info('Processing M3U file', [
                'source' => $this->source,
                'is_url' => $this->isUrl,
                'user_id' => $this->userId,
            ]);

            $results = [];
            $importId = null;

            if ($this->isUrl) {
                // Process from URL
                $results = $m3uParserService->importFromUrl($this->source, $this->userId);

                // Get the import ID from the results
                if (isset($results['import_id'])) {
                    $importId = $results['import_id'];
                } else {
                    // Try to find the import by URL and user ID
                    $import = $m3uImportRepository->findByUrlAndUser($this->source, $this->userId);
                    if ($import) {
                        $importId = $import->id;
                    }
                }
            } else {
                // Process from local file
                if (Storage::exists($this->source)) {
                    $file = new \Illuminate\Http\UploadedFile(
                        Storage::path($this->source),
                        basename($this->source),
                        Storage::mimeType($this->source),
                        null,
                        true
                    );
                    $results = $m3uParserService->importFromFile($file, $this->userId);

                    // Get the import ID from the results
                    if (isset($results['import_id'])) {
                        $importId = $results['import_id'];
                    }

                    // Clean up the file
                    Storage::delete($this->source);
                } else {
                    throw new \Exception("File not found: {$this->source}");
                }
            }

            Log::info('M3U processing completed', [
                'source' => $this->source,
                'results' => $results,
                'import_id' => $importId,
            ]);

            // Update the last validated timestamp
            if ($importId) {
                $m3uImportRepository->updateLastValidated($importId);
                Log::info('Updated last validated timestamp', [
                    'import_id' => $importId,
                ]);
            }

            // Queue validation jobs for imported streams
            if (!empty($results['data']['stream_ids'])) {
                foreach ($results['data']['stream_ids'] as $streamId) {
                    ValidateStreamJob::dispatch($streamId)->delay(now()->addSeconds(5));
                }
            }
        } catch (\Exception $e) {
            Log::error('M3U processing failed', [
                'source' => $this->source,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('M3U processing job failed', [
            'source' => $this->source,
            'error' => $exception->getMessage(),
        ]);
    }
}
