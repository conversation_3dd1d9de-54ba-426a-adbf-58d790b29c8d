<?php

namespace App\Policies;

use App\Models\Stream;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class StreamPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view streams
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Stream $stream): bool
    {
        // Users can view their own streams or streams in public playlists
        if ($user->id === $stream->user_id) {
            return true;
        }

        // Check if the stream is in a public playlist
        return $stream->playlists()
            ->where('is_public', true)
            ->exists();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // All authenticated users can create streams
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Stream $stream): bool
    {
        // Only the owner can update the stream
        return $user->id === $stream->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Stream $stream): bool
    {
        // Only the owner can delete the stream
        return $user->id === $stream->user_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Stream $stream): bool
    {
        // Only the owner can restore the stream
        return $user->id === $stream->user_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Stream $stream): bool
    {
        // Only the owner can force delete the stream
        return $user->id === $stream->user_id;
    }

    /**
     * Determine whether the user can validate the model.
     */
    public function validate(User $user, Stream $stream): bool
    {
        // Only the owner can validate the stream
        return $user->id === $stream->user_id;
    }
}
