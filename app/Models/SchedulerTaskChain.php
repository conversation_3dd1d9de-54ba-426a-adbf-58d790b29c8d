<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SchedulerTaskChain extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'parent_task_id',
        'child_task_id',
        'order',
    ];

    /**
     * Get the parent task.
     */
    public function parentTask(): BelongsTo
    {
        return $this->belongsTo(SchedulerTask::class, 'parent_task_id');
    }

    /**
     * Get the child task.
     */
    public function childTask(): BelongsTo
    {
        return $this->belongsTo(SchedulerTask::class, 'child_task_id');
    }
}
