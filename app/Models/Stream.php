<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Stream extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'normalized_name',
        'url',
        'type',
        'status',
        'metadata',
        'last_checked_at',
        'user_id',
        'stream_cluster_id',
        'cluster_priority',
        'tvg_id',
        'tvg_name',
        'tvg_logo',
        'group_title',
        'quality',
        'bitrate',
        'codec',
        'exclude_from_export',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'last_checked_at' => 'datetime',
        'exclude_from_export' => 'boolean',
    ];

    /**
     * Get the user that owns the stream.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the cluster that the stream belongs to.
     */
    public function cluster(): BelongsTo
    {
        return $this->belongsTo(StreamCluster::class, 'stream_cluster_id');
    }

    /**
     * The playlists that belong to the stream.
     */
    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class)
            ->withPivot('position', 'is_cluster_head', 'stream_cluster_id')
            ->withTimestamps();
    }

    /**
     * The categories that belong to the stream.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class)
            ->withTimestamps();
    }

    /**
     * The tags that belong to the stream.
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)
            ->withTimestamps();
    }

    /**
     * The M3U imports that this stream belongs to.
     */
    public function m3uImports(): BelongsToMany
    {
        return $this->belongsToMany(M3UImport::class, 'm3u_import_stream', 'stream_id', 'm3u_import_id')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include online streams.
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope a query to only include offline streams.
     */
    public function scopeOffline($query)
    {
        return $query->where('status', 'offline');
    }

    /**
     * Scope a query to only include pending streams.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include error streams.
     */
    public function scopeError($query)
    {
        return $query->where('status', 'error');
    }

    /**
     * Get the logo URL for the stream.
     * Returns a default placeholder if no logo is set.
     *
     * @return string
     */
    public function getTvgLogoAttribute($value)
    {
        return $value ?: asset('images/stream-placeholder.png');
    }
}
