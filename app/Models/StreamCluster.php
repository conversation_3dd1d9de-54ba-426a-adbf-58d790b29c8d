<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class StreamCluster extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'normalized_name',
        'status',
        'user_id',
        'category_id',
        'tvg_logo',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns the stream cluster.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that the stream cluster belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the streams that belong to the cluster.
     */
    public function streams(): HasMany
    {
        return $this->hasMany(Stream::class)
            ->orderBy('cluster_priority', 'asc')
            ->orderBy('status', 'asc'); // 'online' comes before 'offline' alphabetically
    }

    /**
     * Scope a query to only include online clusters.
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope a query to only include offline clusters.
     */
    public function scopeOffline($query)
    {
        return $query->where('status', 'offline');
    }

    /**
     * Get the logo URL for the cluster.
     * Returns a default placeholder if no logo is set.
     *
     * @return string
     */
    public function getTvgLogoAttribute($value)
    {
        return $value ?: asset('images/stream-placeholder.png');
    }
}
