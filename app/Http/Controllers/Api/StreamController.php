<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Stream;
use App\Repositories\StreamRepository;
use Illuminate\Http\Request;

class StreamController extends Controller
{
    /**
     * @var StreamRepository
     */
    protected $streamRepository;

    /**
     * StreamController constructor.
     *
     * @param StreamRepository $streamRepository
     */
    public function __construct(StreamRepository $streamRepository)
    {
        $this->streamRepository = $streamRepository;
    }

    /**
     * Get online streams for the authenticated user.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOnlineStreams(Request $request)
    {
        // Get online streams for the authenticated user
        $streams = Stream::where('user_id', $request->user()->id)
            ->where('status', 'online')
            ->orderBy('name')
            ->limit(20)
            ->get(['id', 'name', 'type', 'status', 'tvg_logo']);

        return response()->json($streams);
    }
}
