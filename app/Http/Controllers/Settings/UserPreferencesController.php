<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserPreferencesController extends Controller
{
    /**
     * Show the user preferences page.
     */
    public function index(Request $request): Response
    {
        return Inertia::render('settings/preferences', [
            'preferences' => $request->user()->getPreferences(),
        ]);
    }

    /**
     * Update the user preferences.
     */
    public function update(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'playback' => 'required|array',
            'playback.default_volume' => 'required|integer|min:0|max:100',
            'playback.autoplay' => 'required|boolean',
            'playback.default_speed' => 'required|numeric|min:0.5|max:2',
            'playback.mute_by_default' => 'required|boolean',
            'playback.remember_position' => 'required|boolean',

            'ui' => 'required|array',
            'ui.default_view_mode' => 'required|string|in:grid,list',
            'ui.items_per_page' => 'required|integer|in:10,25,50,100',
            'ui.default_sorting' => 'required|string|in:name,date_added,last_checked',
            'ui.default_sorting_direction' => 'required|string|in:asc,desc',
            'ui.show_stream_details_on_hover' => 'required|boolean',

            'notifications' => 'required|array',
            'notifications.email_notifications' => 'required|boolean',
            'notifications.browser_notifications' => 'required|boolean',
            'notifications.notification_frequency' => 'required|string|in:immediate,daily,weekly',
        ]);

        $request->user()->update([
            'preferences' => $validated,
        ]);

        return redirect()->route('preferences.index')
            ->with('success', 'Preferences updated successfully.');
    }
}
