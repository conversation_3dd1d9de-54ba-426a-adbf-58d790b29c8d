<?php

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PlaylistController;
use App\Http\Controllers\StreamController;
use App\Http\Controllers\StreamClusterController;
use App\Http\Controllers\StreamScheduleController;
use App\Http\Controllers\SchedulerTaskController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\PlayerController;
use App\Http\Controllers\UrlListImportController;

use App\Http\Controllers\Api\StreamController as ApiStreamController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public routes
Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Offline page for PWA
Route::get('/offline', function () {
    return Inertia::render('offline');
})->name('offline');

// Public playlist sharing
Route::get('playlists/shared/{playlist:share_token}', [PlaylistController::class, 'showShared'])
    ->name('playlists.share');

// Export playlist as M3U file
Route::get('playlists/{playlist}/export', [PlaylistController::class, 'export'])
    ->name('playlists.export');

// Raw M3U playlist content
Route::get('playlists/{playlist}/raw', [PlaylistController::class, 'raw'])
    ->name('playlists.raw');

// Stream proxy routes (need to be accessible by the player)
Route::get('player/{stream}/proxy', [PlayerController::class, 'proxy'])
    ->name('player.proxy')
    ->middleware('signed');

// HLS segment proxy route
Route::get('player/{stream}/proxy/segment', [PlayerController::class, 'proxySegment'])
    ->name('player.proxy.segment')
    ->middleware('signed');

// Direct HLS segment proxy route (for segments that don't use the query parameter format)
Route::get('player/{stream}/proxy/{segment}', [PlayerController::class, 'proxyDirectSegment'])
    ->where('segment', '.*');

// Authentication required routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
        ->name('dashboard');
    Route::get('dashboard/data', [App\Http\Controllers\DashboardController::class, 'getData'])
        ->name('dashboard.data');

    // Streams
    Route::resource('streams', StreamController::class);
    Route::post('streams/{stream}/validate', [StreamController::class, 'validate'])
        ->name('streams.validate');
    Route::post('streams/validate-all', [StreamController::class, 'validateAll'])
        ->name('streams.validate-all');
    Route::post('streams/bulk-delete', [StreamController::class, 'bulkDelete'])
        ->name('streams.bulk-delete');
    Route::post('streams/bulk-validate', [StreamController::class, 'bulkValidate'])
        ->name('streams.bulk-validate');
    Route::post('streams/delete-all', [StreamController::class, 'deleteAll'])
        ->name('streams.delete-all');

    // Export
    Route::get('export', [StreamController::class, 'exportIndex'])
        ->name('export.index');
    Route::get('export/streams', [StreamController::class, 'getStreamsForExport'])
        ->name('export.streams');
    Route::post('export', [StreamController::class, 'export'])
        ->name('export.generate');
    Route::get('export/{exportJob}/download', [StreamController::class, 'downloadExport'])
        ->name('export.download');
    Route::delete('export/{exportJob}', [StreamController::class, 'deleteExport'])
        ->name('export.delete');

    // Stream Export Exclusion
    Route::patch('streams/{stream}/toggle-exclude', [StreamController::class, 'toggleExcludeFromExport'])
        ->name('streams.toggle-exclude');
    Route::post('streams/bulk-toggle-exclude', [StreamController::class, 'bulkToggleExcludeFromExport'])
        ->name('streams.bulk-toggle-exclude');

    // Stream Schedules
    Route::get('stream-schedules', [StreamScheduleController::class, 'get'])
        ->name('stream-schedules.get');
    Route::post('stream-schedules', [StreamScheduleController::class, 'store'])
        ->name('stream-schedules.store');
    Route::post('stream-schedules/toggle', [StreamScheduleController::class, 'toggle'])
        ->name('stream-schedules.toggle');

    // Scheduler Page
    Route::get('scheduler', [SchedulerTaskController::class, 'index'])
        ->name('scheduler.index');
    Route::post('scheduler', [SchedulerTaskController::class, 'store'])
        ->name('scheduler.store');
    Route::put('scheduler/{task}', [SchedulerTaskController::class, 'update'])
        ->name('scheduler.update');
    Route::delete('scheduler/{task}', [SchedulerTaskController::class, 'destroy'])
        ->name('scheduler.destroy');
    Route::post('scheduler/{task}/toggle', [SchedulerTaskController::class, 'toggle'])
        ->name('scheduler.toggle');
    Route::post('scheduler/{task}/run-now', [SchedulerTaskController::class, 'runNow'])
        ->name('scheduler.run-now');

    // Playlists
    Route::resource('playlists', PlaylistController::class);
    Route::get('playlists/{playlist}/editor', [PlaylistController::class, 'editor'])
        ->name('playlists.editor');
    Route::post('playlists/{playlist}/add-stream', [PlaylistController::class, 'addStream'])
        ->name('playlists.add-stream');
    Route::delete('playlists/{playlist}/remove-stream/{stream}', [PlaylistController::class, 'removeStream'])
        ->name('playlists.remove-stream');
    Route::post('playlists/{playlist}/reorder', [PlaylistController::class, 'reorder'])
        ->name('playlists.reorder');
    Route::patch('playlists/{playlist}/toggle-visibility', [PlaylistController::class, 'toggleVisibility'])
        ->name('playlists.toggle-visibility');
    Route::post('playlists/delete-all', [PlaylistController::class, 'deleteAll'])
        ->name('playlists.delete-all');
    Route::get('playlists/cluster/{clusterId}/streams', [PlaylistController::class, 'getClusterStreams'])
        ->name('playlists.cluster-streams');

    // Categories
    Route::resource('categories', CategoryController::class);
    Route::post('categories/{category}/add-stream', [CategoryController::class, 'addStream'])
        ->name('categories.add-stream');
    Route::delete('categories/{category}/remove-stream/{stream}', [CategoryController::class, 'removeStream'])
        ->name('categories.remove-stream');

    // Tags
    Route::resource('tags', TagController::class);
    Route::post('tags/{tag}/add-stream', [TagController::class, 'addStream'])
        ->name('tags.add-stream');
    Route::delete('tags/{tag}/remove-stream/{stream}', [TagController::class, 'removeStream'])
        ->name('tags.remove-stream');

    // Cluster management
    Route::post('streams/run-clustering', [StreamController::class, 'runClustering'])
        ->name('streams.run-clustering');
    Route::get('streams/cluster/{clusterId}/streams', [StreamController::class, 'getClusterStreams'])
        ->name('streams.cluster-streams');

    // Stream Clusters
    Route::get('clusters', [StreamClusterController::class, 'index'])
        ->name('clusters.index');
    Route::get('clusters/{cluster}', [StreamClusterController::class, 'show'])
        ->name('clusters.show');
    Route::post('clusters/run', [StreamClusterController::class, 'runClustering'])
        ->name('clusters.run');

    // Import
    Route::get('import', [ImportController::class, 'index'])
        ->name('import.index');
    Route::post('import/url', [ImportController::class, 'importFromUrl'])
        ->name('import.url');
    Route::post('import/bulk-url', [ImportController::class, 'importFromBulkUrl'])
        ->name('import.bulk-url');
    Route::post('import/file', [ImportController::class, 'importFromFile'])
        ->name('import.file');
    Route::post('import/bulk-file', [ImportController::class, 'importFromBulkFile'])
        ->name('import.bulk-file');
    Route::post('import/{import}/refetch', [ImportController::class, 'refetch'])
        ->name('import.refetch');
    Route::post('import/{import}/revalidate', [ImportController::class, 'revalidate'])
        ->name('import.revalidate');
    Route::delete('import/{import}', [ImportController::class, 'remove'])
        ->name('import.remove');
    Route::patch('import/{import}', [ImportController::class, 'update'])
        ->name('import.update');
    Route::patch('import/{import}/update-source', [ImportController::class, 'updateSource'])
        ->name('import.update-source');
    Route::post('import/bulk-delete', [ImportController::class, 'bulkDelete'])
        ->name('import.bulk-delete');
    Route::post('import/delete-all', [ImportController::class, 'deleteAll'])
        ->name('import.delete-all');

    // URL List Imports
    Route::post('url-list-import', [UrlListImportController::class, 'store'])
        ->name('url-list-import.store');
    Route::get('url-list-import', [UrlListImportController::class, 'index'])
        ->name('url-list-import.index');
    Route::get('url-list-import/{id}', [UrlListImportController::class, 'show'])
        ->name('url-list-import.show');
    Route::patch('url-list-import/{id}', [UrlListImportController::class, 'update'])
        ->name('url-list-import.update');
    Route::post('url-list-import/{id}/toggle', [UrlListImportController::class, 'toggle'])
        ->name('url-list-import.toggle');
    Route::post('url-list-import/{id}/run', [UrlListImportController::class, 'run'])
        ->name('url-list-import.run');
    Route::delete('url-list-import/{id}', [UrlListImportController::class, 'destroy'])
        ->name('url-list-import.destroy');

    // Player
    Route::get('player/{stream}', [PlayerController::class, 'show'])
        ->name('player.show');
    Route::post('player/{stream}/report-issue', [PlayerController::class, 'reportIssue'])
        ->name('player.report-issue');
    Route::post('player/{stream}/report-cors-issue', [PlayerController::class, 'reportCorsIssue'])
        ->name('player.report-cors-issue');
    Route::get('player/{stream}/manifest', [PlayerController::class, 'manifest'])
        ->name('player.manifest');

    // GitHub Credentials
    Route::post('github-credentials', [\App\Http\Controllers\GitHubCredentialsController::class, 'store'])
        ->name('github-credentials.store');
    Route::get('github-credentials', [\App\Http\Controllers\GitHubCredentialsController::class, 'index'])
        ->name('github-credentials.index');
    Route::get('github-credentials/{credential_id}/repositories', [\App\Http\Controllers\GitHubCredentialsController::class, 'getRepositories'])
        ->name('github-credentials.repositories');
    Route::get('github-credentials/{credential_id}/branches', [\App\Http\Controllers\GitHubCredentialsController::class, 'getBranches'])
        ->name('github-credentials.branches');
    Route::delete('github-credentials/{id}', [\App\Http\Controllers\GitHubCredentialsController::class, 'destroy'])
        ->name('github-credentials.destroy');

    // Preferred Channels
    Route::get('preferred-channels', [\App\Http\Controllers\PreferredChannelsController::class, 'index'])
        ->name('preferred-channels.index');
    Route::post('preferred-channels/url', [\App\Http\Controllers\PreferredChannelsController::class, 'storeFromUrl'])
        ->name('preferred-channels.store-url');
    Route::post('preferred-channels/json', [\App\Http\Controllers\PreferredChannelsController::class, 'storeFromJson'])
        ->name('preferred-channels.store-json');
    Route::post('preferred-channels/file', [\App\Http\Controllers\PreferredChannelsController::class, 'storeFromFile'])
        ->name('preferred-channels.store-file');
    Route::get('preferred-channels/example', [\App\Http\Controllers\PreferredChannelsController::class, 'getExample'])
        ->name('preferred-channels.example');
    Route::delete('preferred-channels/{id}', [\App\Http\Controllers\PreferredChannelsController::class, 'destroy'])
        ->name('preferred-channels.destroy');

    // API Routes
    Route::get('api/streams/online', [ApiStreamController::class, 'getOnlineStreams'])
        ->name('api.streams.online');
});

// Settings and authentication routes
require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
