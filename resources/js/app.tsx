import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';
import { AutoRefreshProvider } from './contexts/auto-refresh-context';
import { SidebarProvider } from './contexts/sidebar-context';
import PWAInstallBanner from './components/pwa/pwa-install-banner';

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => {
        const pages = import.meta.glob('./pages/**/*.tsx', { eager: true });
        const page = pages[`./pages/${name}.tsx`];
        if (page) {
            return page;
        }
        return resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob('./pages/**/*.tsx', { eager: true }));
    },
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <SidebarProvider>
                <AutoRefreshProvider>
                    <App {...props} />
                    <PWAInstallBanner />
                </AutoRefreshProvider>
            </SidebarProvider>
        );
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on load...
initializeTheme();
