import { render as tl<PERSON><PERSON> } from '@testing-library/react';
import { AutoRefreshProvider } from '@/contexts/auto-refresh-context';
import { ReactNode } from 'react';
import userEvent from '@testing-library/user-event';

// Custom render function that includes providers
export function render(ui: ReactNode, options = {}) {
  return {
    ...tlRender(
      <AutoRefreshProvider>
        {ui}
      </AutoRefreshProvider>,
      options
    ),
    user: userEvent.setup(),
  };
}

// Helper to create a mock InertiaPage
export function createMockPage(overrides = {}) {
  return {
    props: {
      auth: { user: { id: 1, name: 'Test User', email: '<EMAIL>' } },
      errors: {},
      flash: {},
      ...overrides,
    },
  };
}

// Re-export everything from testing-library
export * from '@testing-library/react';
