import React, { useState, useEffect } from 'react';
import { useForm, router } from '@inertiajs/react';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { GitHubCredential } from '@/types/github';
import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface GitHubCredentialsFormProps {
  credentials: GitHubCredential[];
  onCredentialsChange?: () => void;
}

export default function GitHubCredentialsForm({ credentials, onCredentialsChange }: GitHubCredentialsFormProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    name: '',
    token: '',
  });

  const [credentialToDelete, setCredentialToDelete] = useState<GitHubCredential | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('github-credentials.store'), {
      onSuccess: () => {
        reset('name', 'token');
        toast.success('GitHub credential added successfully');
        if (onCredentialsChange) {
          onCredentialsChange();
        }
      },
      onError: () => {
        toast.error('Failed to add GitHub credential');
      },
    });
  };

  const handleDelete = (credential: GitHubCredential) => {
    setCredentialToDelete(credential);
  };

  const confirmDelete = () => {
    if (!credentialToDelete) return;

    router.delete(route('github-credentials.destroy', credentialToDelete.id), {
      onSuccess: () => {
        toast.success('GitHub credential deleted successfully');
        if (onCredentialsChange) {
          onCredentialsChange();
        }
        setCredentialToDelete(null);
      },
      onError: () => {
        toast.error('Failed to delete GitHub credential');
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>GitHub Credentials</CardTitle>
        <CardDescription>
          Add your GitHub personal access token to push exports directly to your repositories.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Credential Name</Label>
            <Input
              id="name"
              value={data.name}
              onChange={(e) => setData('name', e.target.value)}
              placeholder="My GitHub Token"
              required
            />
            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="token">Personal Access Token</Label>
            <Input
              id="token"
              type="password"
              value={data.token}
              onChange={(e) => setData('token', e.target.value)}
              placeholder="ghp_..."
              required
            />
            {errors.token && <p className="text-sm text-red-500">{errors.token}</p>}
            <p className="text-xs text-muted-foreground">
              Create a token with 'repo' scope at{' '}
              <a
                href="https://github.com/settings/tokens"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                GitHub Settings
              </a>
            </p>
          </div>

          <Button type="submit" disabled={processing}>
            Add Credential
          </Button>
        </form>

        {credentials.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium mb-2">Your Credentials</h3>
            <div className="space-y-2">
              {credentials.map((credential) => (
                <div
                  key={credential.id}
                  className="flex items-center justify-between p-2 rounded-md border"
                >
                  <div>
                    <p className="font-medium">{credential.name}</p>
                    <p className="text-xs text-muted-foreground">Token: {credential.token_hint}</p>
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(credential)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will permanently delete this GitHub credential.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
