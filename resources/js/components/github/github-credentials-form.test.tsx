import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import GitHubCredentialsForm from './github-credentials-form';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { toast } from 'sonner';

// Mock dependencies
vi.mock('sonner');

// Mock Inertia useForm and router
vi.mock('@inertiajs/react', () => ({
  useForm: () => ({
    data: { name: '', token: '' },
    setData: vi.fn(),
    post: vi.fn(),
    processing: false,
    errors: {},
    reset: vi.fn(),
  }),
  router: {
    delete: vi.fn(),
  },
}));

// Mock route function
global.route = vi.fn((name, params) => {
  if (name === 'github-credentials.store') {
    return '/github-credentials/store';
  }
  if (name === 'github-credentials.destroy') {
    return `/github-credentials/destroy/${params}`;
  }
  return `/${name}`;
});

// Create test fixtures
const createCredential = (overrides = {}) => ({
  id: 1,
  name: 'Test Credential',
  token_hint: 'ghp_...1234',
  ...overrides
});

describe('GitHubCredentialsForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form with no credentials', () => {
    render(<GitHubCredentialsForm credentials={[]} />);

    expect(screen.getByText('GitHub Credentials')).toBeInTheDocument();
    expect(screen.getByLabelText('Credential Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Personal Access Token')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Add Credential' })).toBeInTheDocument();
    expect(screen.queryByText('Your Credentials')).not.toBeInTheDocument();
  });

  it('renders the form with existing credentials', () => {
    const credentials = [
      createCredential(),
      createCredential({ id: 2, name: 'Another Credential', token_hint: 'ghp_...5678' })
    ];

    render(<GitHubCredentialsForm credentials={credentials} />);

    expect(screen.getByText('GitHub Credentials')).toBeInTheDocument();
    expect(screen.getByText('Your Credentials')).toBeInTheDocument();
    expect(screen.getByText('Test Credential')).toBeInTheDocument();
    expect(screen.getByText('Another Credential')).toBeInTheDocument();
    expect(screen.getByText('Token: ghp_...1234')).toBeInTheDocument();
    expect(screen.getByText('Token: ghp_...5678')).toBeInTheDocument();
  });

  it('submits the form with valid data', async () => {
    // Setup success response
    const mockPost = vi.fn().mockImplementation((url, options) => {
      options.onSuccess();
    });
    const mockSetData = vi.fn();
    const mockReset = vi.fn();

    // Override the mock for this test
    vi.mocked('@inertiajs/react').useForm.mockReturnValue({
      data: { name: '', token: '' },
      setData: mockSetData,
      post: mockPost,
      processing: false,
      errors: {},
      reset: mockReset,
    });

    const onCredentialsChange = vi.fn();
    render(<GitHubCredentialsForm credentials={[]} onCredentialsChange={onCredentialsChange} />);

    // Fill out the form
    const nameInput = screen.getByLabelText('Credential Name');
    const tokenInput = screen.getByLabelText('Personal Access Token');

    fireEvent.change(nameInput, { target: { value: 'New Credential' } });
    fireEvent.change(tokenInput, { target: { value: 'ghp_123456789' } });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: 'Add Credential' });
    fireEvent.click(submitButton);

    // Check that the form was submitted correctly
    await waitFor(() => {
      expect(mockSetData).toHaveBeenCalledWith('name', 'New Credential');
      expect(mockSetData).toHaveBeenCalledWith('token', 'ghp_123456789');
      expect(mockPost).toHaveBeenCalledWith('/github-credentials/store', expect.any(Object));
      expect(mockReset).toHaveBeenCalledWith('name', 'token');
      expect(toast.success).toHaveBeenCalledWith('GitHub credential added successfully');
      expect(onCredentialsChange).toHaveBeenCalled();
    });
  });

  it('handles form submission errors', async () => {
    // Setup error response
    const mockPost = vi.fn().mockImplementation((url, options) => {
      options.onError();
    });
    const mockSetData = vi.fn();

    // Override the mock for this test
    vi.mocked('@inertiajs/react').useForm.mockReturnValue({
      data: { name: '', token: '' },
      setData: mockSetData,
      post: mockPost,
      processing: false,
      errors: {},
      reset: vi.fn(),
    });

    render(<GitHubCredentialsForm credentials={[]} />);

    // Fill out the form
    const nameInput = screen.getByLabelText('Credential Name');
    const tokenInput = screen.getByLabelText('Personal Access Token');

    fireEvent.change(nameInput, { target: { value: 'New Credential' } });
    fireEvent.change(tokenInput, { target: { value: 'ghp_123456789' } });

    // Submit the form
    const submitButton = screen.getByRole('button', { name: 'Add Credential' });
    fireEvent.click(submitButton);

    // Check that the error was handled correctly
    await waitFor(() => {
      expect(mockPost).toHaveBeenCalledWith('/github-credentials/store', expect.any(Object));
      expect(toast.error).toHaveBeenCalledWith('Failed to add GitHub credential');
    });
  });

  it('displays validation errors', () => {
    // Mock useForm to return errors
    vi.mocked('@inertiajs/react').useForm.mockReturnValue({
      data: { name: '', token: '' },
      setData: vi.fn(),
      post: vi.fn(),
      processing: false,
      errors: {
        name: 'The name field is required',
        token: 'The token field is required',
      },
      reset: vi.fn(),
    });

    render(<GitHubCredentialsForm credentials={[]} />);

    expect(screen.getByText('The name field is required')).toBeInTheDocument();
    expect(screen.getByText('The token field is required')).toBeInTheDocument();
  });

  it('opens delete confirmation dialog when delete button is clicked', () => {
    const credentials = [createCredential()];

    render(<GitHubCredentialsForm credentials={credentials} />);

    // Find and click the delete button
    const deleteButton = screen.getByRole('button', { name: '' }); // The button has no text, only an icon
    fireEvent.click(deleteButton);

    // Check that the confirmation dialog is shown
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
    expect(screen.getByText('This will permanently delete this GitHub credential.')).toBeInTheDocument();
  });

  it('deletes a credential when confirmed', async () => {
    // Setup success response
    const mockDelete = vi.fn().mockImplementation((url, options) => {
      options.onSuccess();
    });

    // Override the mock for this test
    vi.mocked('@inertiajs/react').router.delete = mockDelete;

    const onCredentialsChange = vi.fn();
    const credentials = [createCredential()];

    render(<GitHubCredentialsForm credentials={credentials} onCredentialsChange={onCredentialsChange} />);

    // Find and click the delete button
    const deleteButton = screen.getByRole('button', { name: '' }); // The button has no text, only an icon
    fireEvent.click(deleteButton);

    // Find and click the confirm button
    const confirmButton = screen.getByRole('button', { name: 'Delete' });
    fireEvent.click(confirmButton);

    // Check that the API was called correctly
    await waitFor(() => {
      expect(mockDelete).toHaveBeenCalledWith('/github-credentials/destroy/1', expect.any(Object));
      expect(toast.success).toHaveBeenCalledWith('GitHub credential deleted successfully');
      expect(onCredentialsChange).toHaveBeenCalled();
    });
  });

  it('handles delete errors', async () => {
    // Setup error response
    const mockDelete = vi.fn().mockImplementation((url, options) => {
      options.onError();
    });

    // Override the mock for this test
    vi.mocked('@inertiajs/react').router.delete = mockDelete;

    const onCredentialsChange = vi.fn();
    const credentials = [createCredential()];

    render(<GitHubCredentialsForm credentials={credentials} onCredentialsChange={onCredentialsChange} />);

    // Find and click the delete button
    const deleteButton = screen.getByRole('button', { name: '' }); // The button has no text, only an icon
    fireEvent.click(deleteButton);

    // Find and click the confirm button
    const confirmButton = screen.getByRole('button', { name: 'Delete' });
    fireEvent.click(confirmButton);

    // Check that the error was handled correctly
    await waitFor(() => {
      expect(mockDelete).toHaveBeenCalledWith('/github-credentials/destroy/1', expect.any(Object));
      expect(toast.error).toHaveBeenCalledWith('Failed to delete GitHub credential');
      expect(onCredentialsChange).not.toHaveBeenCalled();
    });
  });

  it('shows the GitHub settings link', () => {
    render(<GitHubCredentialsForm credentials={[]} />);

    const link = screen.getByText('GitHub Settings');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', 'https://github.com/settings/tokens');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });
});
