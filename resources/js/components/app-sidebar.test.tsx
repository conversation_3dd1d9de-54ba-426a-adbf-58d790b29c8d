import React from 'react';
import { render, screen } from '@testing-library/react';
import AppSidebar from './navigation/app-sidebar';
import { describe, expect, it, vi } from 'vitest';

// Mock Inertia useForm and router
vi.mock('@inertiajs/react', () => ({
  Link: ({ href, children, className }: any) => (
    <a href={href} className={className}>
      {children}
    </a>
  ),
  usePage: () => ({
    props: {
      auth: {
        user: {
          name: 'Test User',
          email: '<EMAIL>',
        },
      },
    },
  }),
}));

// Mock route function
global.route = vi.fn((name) => `/${name}`);

describe('AppSidebar', () => {
  it('renders the sidebar with navigation links', () => {
    render(<AppSidebar />);
    
    // Check that the logo is rendered
    expect(screen.getByText('IPTV Manager')).toBeInTheDocument();
    
    // Check that the navigation links are rendered
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Playlists')).toBeInTheDocument();
    expect(screen.getByText('Streams')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('displays the user avatar', () => {
    render(<AppSidebar />);
    
    // Check that the user avatar is rendered
    const avatar = screen.getByText('TU'); // Test User initials
    expect(avatar).toBeInTheDocument();
  });

  it('includes the darkmode toggle', () => {
    render(<AppSidebar />);
    
    // Check that the darkmode toggle is rendered
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('includes the auto-refresh controls', () => {
    render(<AppSidebar />);
    
    // Check that the auto-refresh controls are rendered
    expect(screen.getByText('Auto-refresh')).toBeInTheDocument();
  });
});
