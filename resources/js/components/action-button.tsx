import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActionButtonProps {
  label: string;
  icon?: LucideIcon;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  method?: 'get' | 'post' | 'put' | 'patch' | 'delete';
  disabled?: boolean;
  className?: string;
  title?: string;
}

/**
 * Action Button Component
 * 
 * Provides a consistent button for actions with:
 * - Label
 * - Optional icon
 * - Link or onClick functionality
 * - Consistent styling
 */
export default function ActionButton({ 
  label, 
  icon: Icon, 
  href, 
  onClick, 
  variant = 'default',
  size = 'default',
  method = 'get',
  disabled = false,
  className,
  title
}: ActionButtonProps) {
  // If href is provided, render as a Link
  if (href) {
    return (
      <Button
        variant={variant}
        size={size}
        disabled={disabled}
        className={className}
        title={title}
        asChild
      >
        <Link
          href={href}
          method={method}
          as={method !== 'get' ? 'button' : undefined}
        >
          {Icon && <Icon className="h-4 w-4 mr-2" />}
          {label}
        </Link>
      </Button>
    );
  }
  
  // Otherwise, render as a regular button
  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      disabled={disabled}
      className={className}
      title={title}
    >
      {Icon && <Icon className="h-4 w-4 mr-2" />}
      {label}
    </Button>
  );
}
