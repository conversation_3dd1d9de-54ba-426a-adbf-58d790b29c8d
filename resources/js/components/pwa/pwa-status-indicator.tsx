import React from 'react';
import { WifiOff, Wifi, RefreshCw, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { usePWA } from '@/hooks/use-pwa';

/**
 * PWA Status Indicator Component
 * 
 * Shows online/offline status and update availability in the sidebar
 */
export default function PWAStatusIndicator() {
  const { isOnline, updateAvailable, updateApp, isInstallable, installApp } = usePWA();

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        {/* Online/Offline Status */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center">
              {isOnline ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isOnline ? 'Online' : 'Offline'}</p>
          </TooltipContent>
        </Tooltip>

        {/* Update Available */}
        {updateAvailable && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                onClick={updateApp}
                className="w-8 h-8 p-0"
              >
                <RefreshCw className="w-4 h-4 text-blue-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Update available - Click to refresh</p>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Install Available */}
        {isInstallable && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                onClick={installApp}
                className="w-8 h-8 p-0"
              >
                <Download className="w-4 h-4 text-primary" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Install app for better experience</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}
