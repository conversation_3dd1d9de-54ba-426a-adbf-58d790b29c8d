import React from 'react';
import { <PERSON> } from '@inertiajs/react';
import { LogOut, Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAppearance } from '@/hooks/use-appearance';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import PWAStatusIndicator from '@/components/pwa/pwa-status-indicator';

interface SidebarFooterProps {
  isMobile?: boolean;
  onItemClick?: () => void;
}

/**
 * Sidebar Footer Component
 *
 * Contains the dark mode toggle and logout button
 */
export default function SidebarFooter({ isMobile = false, onItemClick }: SidebarFooterProps) {
  const { appearance, updateAppearance } = useAppearance();
  
  const toggleTheme = () => {
    updateAppearance(appearance === 'dark' ? 'light' : 'dark');
  };

  // Mobile version
  if (isMobile) {
    return (
      <div className="border-t p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="rounded-md"
            >
              {appearance === 'dark' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
              <span className="sr-only">Toggle theme</span>
            </Button>

            <PWAStatusIndicator />
          </div>

          <Link
            href={route('logout')}
            method="post"
            as="button"
            className="flex items-center justify-center rounded-md px-3 py-2 text-sm text-red-500 hover:bg-muted"
            onClick={onItemClick}
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log Out</span>
          </Link>
        </div>
      </div>
    );
  }

  // Desktop version with tooltips
  return (
    <div className="border-t p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleTheme}
                  className="rounded-md"
                >
                  {appearance === 'dark' ? (
                    <Sun className="h-5 w-5" />
                  ) : (
                    <Moon className="h-5 w-5" />
                  )}
                  <span className="sr-only">Toggle theme</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle {appearance === 'dark' ? 'Light' : 'Dark'} Mode</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <PWAStatusIndicator />
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Link
                href={route('logout')}
                method="post"
                as="button"
                className="flex items-center justify-center rounded-md p-2 text-red-500 hover:bg-muted"
              >
                <LogOut className="h-5 w-5" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <p>Log Out</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
