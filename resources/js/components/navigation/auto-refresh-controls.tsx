import { RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { useAutoRefresh } from '@/contexts/auto-refresh-context';

/**
 * Auto Refresh Controls Component
 *
 * Provides controls for managing auto-refresh functionality.
 */
export default function AutoRefreshControls() {
  const {
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval,
    isRefreshing,
    lastUpdated,
    refreshData,
    isAutoRefreshTemporarilyDisabled
  } = useAutoRefresh();

  // Format the last updated time
  const formattedLastUpdated = lastUpdated
    ? lastUpdated.toLocaleTimeString()
    : 'Never';

  // Handle manual refresh
  const handleRefresh = () => {
    refreshData();
  };

  return (
    <div className="border-t px-4 py-3 space-y-2">
      {isAutoRefreshTemporarilyDisabled ? (
        <div className="text-amber-500 text-xs mb-2">
          Auto-refresh is temporarily disabled on this page.
          It will be restored when you navigate away.
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <Label htmlFor="auto-refresh" className="text-sm">Auto-refresh</Label>
          <Switch
            id="auto-refresh"
            checked={autoRefresh}
            onCheckedChange={setAutoRefresh}
            disabled={isAutoRefreshTemporarilyDisabled}
          />
        </div>
      )}

      {autoRefresh && !isAutoRefreshTemporarilyDisabled && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm">Interval</Label>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRefreshInterval(10000)}
                className={`px-2 h-6 text-xs ${refreshInterval === 10000 ? 'bg-muted' : ''}`}
                disabled={isAutoRefreshTemporarilyDisabled}
              >
                10s
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRefreshInterval(30000)}
                className={`px-2 h-6 text-xs ${refreshInterval === 30000 ? 'bg-muted' : ''}`}
                disabled={isAutoRefreshTemporarilyDisabled}
              >
                30s
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRefreshInterval(60000)}
                className={`px-2 h-6 text-xs ${refreshInterval === 60000 ? 'bg-muted' : ''}`}
                disabled={isAutoRefreshTemporarilyDisabled}
              >
                1m
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing || isAutoRefreshTemporarilyDisabled}
          className="w-full h-7 text-xs"
        >
          <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh Now'}
        </Button>
      </div>

      <div className="text-xs text-muted-foreground">
        Last updated: {formattedLastUpdated}
      </div>
    </div>
  );
}
