import React from 'react';
import { Link } from '@inertiajs/react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { type NavItem } from '@/types';
import { useSidebar } from '@/contexts/sidebar-context';
import { ChevronRight } from 'lucide-react';

interface SidebarNavigationProps {
  items: NavItem[];
  grouped?: boolean;
  title?: string;
  description?: string;
  onItemClick?: () => void;
  variant?: 'default' | 'settings';
}

interface GroupedItems {
  [key: string]: NavItem[];
}

/**
 * Sidebar Navigation Component
 *
 * A unified navigation component that can be used for both the main sidebar
 * and the settings sidebar. Supports grouped or flat navigation items,
 * with or without icons.
 */
export default function SidebarNavigation({
  items,
  grouped = false,
  title,
  description,
  onItemClick,
  variant = 'default'
}: SidebarNavigationProps) {
  // When server-side rendering, we only render the layout on the client
  // to avoid hydration mismatches with route() function
  if (typeof window === 'undefined') {
    return null;
  }

  const currentPath = window.location.pathname;

  // Group items by category if needed
  let groupedItems: GroupedItems = {};
  let categoryOrder: string[] = [];
  let categoryLabels: Record<string, string> = {};

  if (grouped && items.some(item => item.category)) {
    // Extract unique categories and their labels
    items.forEach(item => {
      if (item.category) {
        if (!groupedItems[item.category]) {
          groupedItems[item.category] = [];
          if (!categoryOrder.includes(item.category)) {
            categoryOrder.push(item.category);
          }
        }
        groupedItems[item.category].push(item);
      }
    });

    // Set default category labels if not provided
    categoryLabels = {
      main: 'Main',
      content: 'Content Management',
      data: 'Data Management',
      system: 'System',
      ...categoryLabels
    };
  } else {
    // If not grouped, put all items in a single group
    groupedItems = { 'default': items };
    categoryOrder = ['default'];
  }

  // Determine if an item is active
  const isActive = (href: string | URL) => {
    const hrefString = href.toString();
    const hrefPath = hrefString.replace(window.location.origin, '').split('?')[0];
    return currentPath === hrefPath;
  };

  // Try to get the sidebar context, but provide a fallback if not available
  let toggleSettingsMode = () => {
    console.warn('SidebarNavigation: toggleSettingsMode called but SidebarProvider not found');
    // Fallback: Navigate to the settings page using Inertia
    import('@inertiajs/react').then(({ router }) => {
      router.visit(route('profile.edit'));
    });
  };

  try {
    const sidebarContext = useSidebar();
    toggleSettingsMode = sidebarContext.toggleSettingsMode;
  } catch (error) {
    console.warn('SidebarNavigation: SidebarProvider not found, using fallback');
  }

  // Render navigation items
  const renderNavItems = (navItems: NavItem[]) => {
    return navItems.map((item, index) => (
      <li key={`${item.title}-${index}`}>
        {item.disabled ? (
          // Disabled item with tooltip
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center rounded-lg px-4 py-2 text-sm font-medium text-gray-400 cursor-not-allowed">
                  {item.icon && <item.icon className="mr-3 h-5 w-5" />}
                  {item.title}
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Planned but not implemented</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : item.isSettingsButton ? (
          // Settings button - opens settings navigation
          <Button
            variant="ghost"
            className="flex w-full items-center justify-between rounded-lg px-4 py-2 text-sm font-medium text-muted-foreground hover:bg-muted hover:text-foreground"
            onClick={() => {
              toggleSettingsMode();
              if (onItemClick) onItemClick();
            }}
          >
            <div className="flex items-center">
              {item.icon && <item.icon className="mr-3 h-5 w-5" />}
              {item.title}
            </div>
            <ChevronRight className="h-4 w-4 opacity-70" />
          </Button>
        ) : item.isBackButton ? (
          // Back button - returns to main navigation
          <Button
            variant="ghost"
            className="flex w-full items-center justify-start rounded-lg px-4 py-2 text-sm font-medium text-muted-foreground hover:bg-muted hover:text-foreground"
            onClick={() => {
              toggleSettingsMode();
              if (onItemClick) onItemClick();
            }}
          >
            {item.icon && <item.icon className="mr-3 h-5 w-5" />}
            {item.title}
          </Button>
        ) : (
          // Regular navigation item
          <Link
            href={item.href}
            className={cn(
              'flex items-center rounded-lg px-4 py-2 text-sm font-medium',
              isActive(item.href)
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
            )}
            onClick={onItemClick}
            prefetch
          >
            {item.icon && <item.icon className="mr-3 h-5 w-5" />}
            {item.title}
          </Link>
        )}
      </li>
    ));
  };

  return (
    <div className="space-y-2 py-4">
      {/* Title and description */}
      {title && (
        <div className="mb-4 px-4 py-2">
          <h2 className="text-lg font-bold">{title}</h2>
          {description && <p className="text-xs text-muted-foreground">{description}</p>}
        </div>
      )}

      {/* Navigation items */}
      <ul className="space-y-2">
        {categoryOrder.map((category, categoryIndex) => (
          <React.Fragment key={category}>
            {/* Category separator */}
            {grouped && category !== 'default' && categoryIndex > 0 && (
              <li className="px-4 py-2">
                <div className="flex items-center space-x-2">
                  <div className="h-px flex-1 bg-border"></div>
                  <span className="text-xs font-medium text-muted-foreground">
                    {categoryLabels[category]}
                  </span>
                  <div className="h-px flex-1 bg-border"></div>
                </div>
              </li>
            )}

            {/* Items in this category */}
            {renderNavItems(groupedItems[category] || [])}
          </React.Fragment>
        ))}
      </ul>
    </div>
  );
}
