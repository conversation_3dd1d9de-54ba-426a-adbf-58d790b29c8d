import { LogOut } from 'lucide-react';
import { Link, usePage } from '@inertiajs/react';
import { PageProps } from '@inertiajs/core';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface UserProfileSectionProps {
  isMobile?: boolean;
  onItemClick?: () => void;
}

/**
 * User Profile Section Component
 *
 * Displays user information and provides access to user-related settings.
 */
export default function UserProfileSection({ isMobile = false, onItemClick }: UserProfileSectionProps) {
  const { auth } = usePage<PageProps>().props as any;

  const userInitials = auth.user?.name
    .split(' ')
    .map((n: string) => n[0])
    .join('')
    .toUpperCase();

  // Mobile version of the profile section
  if (isMobile) {
    return (
      <div className="border-t p-4">
        <Link
          href={route('logout')}
          method="post"
          as="button"
          className="flex w-full items-center justify-center rounded-md px-3 py-2 text-sm text-red-500 hover:bg-muted"
          onClick={onItemClick}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log Out</span>
        </Link>
      </div>
    );
  }

  // Desktop version with tooltip
  return (
    <div className="border-t p-4">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link
              href={route('logout')}
              method="post"
              as="button"
              className="flex w-full items-center justify-center rounded-md p-2 text-red-500 hover:bg-muted"
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={auth.user?.profile_photo_url} />
                <AvatarFallback>{userInitials}</AvatarFallback>
              </Avatar>
            </Link>
          </TooltipTrigger>
          <TooltipContent>
            <p>Log Out</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
