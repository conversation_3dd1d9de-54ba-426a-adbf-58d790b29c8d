import React, { useEffect, useRef, useState, useCallback } from 'react';
import Hls from 'hls.js';
import axios from 'axios';
import {
  Settings,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  SkipBack,
  SkipForward,
  FastForward,
  Rewind
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Slider } from '@/components/ui/slider';

interface QualityLevel {
  level: number;
  height: number;
  width: number;
  bitrate: number;
  name: string;
  url: string;
}

interface VideoPlayerProps {
  stream: {
    id: number;
    name: string;
    url: string;
    type: string;
    status: string;
    proxy_url: string | null;
    metadata?: {
      is_master_playlist?: boolean;
      variants?: Array<{
        resolution?: string;
        bitrate?: number;
        quality?: string;
        codec?: string;
        url?: string;
      }>;
      [key: string]: any;
    };
  };
  onError?: (error: string) => void;
  initialError?: string;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ stream, onError, initialError, className }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const controlsTimeout = useRef<NodeJS.Timeout | null>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(initialError || null);
  const [isLoading, setIsLoading] = useState(!initialError);
  const [usedProxyUrl, setUsedProxyUrl] = useState<boolean>(false);
  const [qualityLevels, setQualityLevels] = useState<QualityLevel[]>([]);
  const [currentQuality, setCurrentQuality] = useState<number>(-1);
  const [isAutoQuality, setIsAutoQuality] = useState<boolean>(true);
  const [usingFallbackPlayer, setUsingFallbackPlayer] = useState<boolean>(false);
  const [showControls, setShowControls] = useState<boolean>(false);

  // Custom controls state
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(true); // Start muted to allow autoplay
  const [volume, setVolume] = useState<number>(1);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [playbackRate, setPlaybackRate] = useState<number>(1);
  const [userInteracted, setUserInteracted] = useState<boolean>(false);
  const [autoplayBlocked, setAutoplayBlocked] = useState<boolean>(false);

  // Function to change quality level
  const changeQuality = useCallback((level: number) => {
    if (!hlsRef.current) return;

    // If level is -1, enable auto quality selection
    if (level === -1) {
      hlsRef.current.currentLevel = -1;
      setIsAutoQuality(true);
    } else {
      // Otherwise, set to specific level
      hlsRef.current.currentLevel = level;
      setIsAutoQuality(false);
    }

    setCurrentQuality(level);
  }, []);

  // Custom controls handlers
  const togglePlay = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    // Mark that user has interacted with the player
    setUserInteracted(true);

    // If autoplay was blocked and video is muted, unmute it when user clicks play
    if (autoplayBlocked && isMuted) {
      video.muted = false;
      setIsMuted(false);
    }

    if (video.paused) {
      // Use async/await to properly handle play() promise
      (async () => {
        try {
          await video.play();
          setIsPlaying(true);
          // Clear autoplay blocked state if play succeeds
          setAutoplayBlocked(false);
        } catch (error) {
          // Check if this is an AbortError (play interrupted by new load request)
          if (error instanceof DOMException && error.name === 'AbortError') {
            console.log('Play request was interrupted by a new load request');
            // Don't show error to user as this is expected when changing sources
          } else if (error instanceof DOMException && error.name === 'NotAllowedError') {
            console.log('Play request was blocked by browser autoplay policy');
            setAutoplayBlocked(true);
            // Don't show error to user as we'll show a play button overlay instead
          } else {
            console.error('Error playing video:', error);
            handleError(error);
          }
        }
      })();
    } else {
      video.pause();
      setIsPlaying(false);
    }
  }, [isMuted, autoplayBlocked]);

  const toggleMute = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  }, []);

  const handleVolumeChange = useCallback((value: number[]) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = value[0];
    video.volume = newVolume;
    setVolume(newVolume);

    if (newVolume === 0) {
      video.muted = true;
      setIsMuted(true);
    } else if (isMuted) {
      video.muted = false;
      setIsMuted(false);
    }
  }, [isMuted]);

  const handleTimeUpdate = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    setCurrentTime(video.currentTime);
    setDuration(video.duration);
  }, []);

  const handleSeek = useCallback((value: number[]) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = value[0];
    setCurrentTime(value[0]);
  }, []);

  const handleProgressClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current;
    const progress = progressRef.current;
    if (!video || !progress) return;

    const rect = progress.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    video.currentTime = pos * video.duration;
  }, []);

  const toggleFullscreen = useCallback(() => {
    const container = containerRef.current;
    if (!container) return;

    if (!document.fullscreenElement) {
      container.requestFullscreen().then(() => {
        setIsFullscreen(true);
      }).catch((error) => {
        console.error('Error attempting to enable fullscreen:', error);
      });
    } else {
      document.exitFullscreen().then(() => {
        setIsFullscreen(false);
      }).catch((error) => {
        console.error('Error attempting to exit fullscreen:', error);
      });
    }
  }, []);

  const changePlaybackRate = useCallback((rate: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.playbackRate = rate;
    setPlaybackRate(rate);
  }, []);

  const skipForward = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = Math.min(video.currentTime + 10, video.duration);
  }, []);

  const skipBackward = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = Math.max(video.currentTime - 10, 0);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // Reset quality levels when stream changes
    setQualityLevels([]);
    setCurrentQuality(-1);
    setIsAutoQuality(true);

    const reportCorsIssue = async () => {
      try {
        // Only report if we're not already using the proxy
        if (!usedProxyUrl && stream.proxy_url) {
          console.log('Reporting CORS issue for stream:', stream.id);
          await axios.post(route('player.report-cors-issue', { stream: stream.id }));
        }
      } catch (err) {
        console.error('Failed to report CORS issue:', err);
      }
    };

    const handleError = (e: any) => {
      console.error('Video playback error:', e);

      // Check if this is a CORS error or network error
      const errorMessage = e.message || 'Unknown error';
      const isCorsError =
        errorMessage.includes('CORS') ||
        errorMessage.includes('cross-origin') ||
        errorMessage.includes('Access-Control-Allow-Origin') ||
        errorMessage.includes('blocked by CORS policy') ||
        (e.name === 'MediaError' && e.code === 2); // MEDIA_ERR_NETWORK

      const isNetworkError =
        (e.details && (
          e.details.includes('networkError') ||
          e.details.includes('manifestLoadError') ||
          e.details.includes('fragLoadError') ||
          e.details.includes('404') ||
          e.details.includes('timeout')
        )) ||
        errorMessage.includes('network') ||
        errorMessage.includes('failed to load') ||
        errorMessage.includes('404') ||
        errorMessage.includes('timeout');

      // If we have an error and a proxy URL but aren't using it yet, try the proxy
      if ((isCorsError || isNetworkError) && stream.proxy_url && !usedProxyUrl) {
        console.log('Streaming error detected, switching to proxy URL');
        reportCorsIssue();

        // Switch to proxy URL
        setUsedProxyUrl(true);

        // Retry with proxy URL
        if (stream.type === 'hls') {
          setupHlsPlayer(stream.proxy_url);
        } else {
          setupDirectPlayer(stream.proxy_url);
        }

        return;
      }

      // If we're already using the proxy or don't have one, show the error
      const displayErrorMessage = `Error playing stream: ${errorMessage}`;
      setError(displayErrorMessage);
      if (onError) onError(displayErrorMessage);

      // If this is a CORS or network error and we don't have a proxy URL, report it
      if ((isCorsError || isNetworkError) && !stream.proxy_url) {
        reportCorsIssue();
      }
    };

    const setupHlsPlayer = (url: string) => {
      if (Hls.isSupported()) {
        // Destroy previous instance if it exists
        if (hlsRef.current) {
          hlsRef.current.destroy();
        }

        // Create new HLS instance with configuration
        // Add specific config for TS streams if needed
        const hlsConfig = {
          maxBufferLength: 30,
          maxMaxBufferLength: 60,
          manifestLoadingTimeOut: 10000,
          manifestLoadingMaxRetry: 3,
          levelLoadingTimeOut: 10000,
          levelLoadingMaxRetry: 3,
          fragLoadingTimeOut: 20000,
          fragLoadingMaxRetry: 3,
          // Start with auto quality selection
          startLevel: -1,
          // Enable capability to choose quality
          capLevelToPlayerSize: false,
        };

        // For TS streams, add specific configuration
        if (stream.type === 'ts') {
          // Extend config with TS-specific settings
          Object.assign(hlsConfig, {
            // Increase timeouts and retries for TS streams
            manifestLoadingTimeOut: 15000,
            manifestLoadingMaxRetry: 5,
            fragLoadingTimeOut: 30000,
            fragLoadingMaxRetry: 5,
          });
        }

        const hls = new Hls(hlsConfig);

        // Store HLS instance in ref for later access
        hlsRef.current = hls;

        hls.loadSource(url);
        hls.attachMedia(video);

        // Handle manifest parsed event to extract quality levels
        hls.on(Hls.Events.MANIFEST_PARSED, (_event, data) => {
          // Extract quality levels from HLS data
          if (data.levels && data.levels.length > 0) {
            const levels = data.levels.map((level, index) => {
              // Extract resolution from level
              const width = level.width || 0;
              const height = level.height || 0;
              const bitrate = level.bitrate || 0;

              // Create a readable name for the quality level
              let name = 'Auto';
              if (height >= 2160) name = '4K';
              else if (height >= 1440) name = '2K';
              else if (height >= 1080) name = 'FHD';
              else if (height >= 720) name = 'HD';
              else if (height >= 480) name = 'SD';
              else if (height > 0) name = `${height}p`;
              else if (bitrate > 0) name = `${Math.round(bitrate / 1000)} Kbps`;
              else name = `Level ${index}`;

              return {
                level: index,
                width,
                height,
                bitrate,
                name,
                url: Array.isArray(level.url) ? level.url[0] : level.url
              };
            });

            // Sort levels by bitrate (highest first)
            const sortedLevels = [...levels].sort((a, b) => b.bitrate - a.bitrate);

            // Update state with available quality levels
            setQualityLevels(sortedLevels);
          }

          setIsLoading(false);
          // Use async/await to properly handle play() promise
          (async () => {
            try {
              await video.play();
              setIsPlaying(true);
              setAutoplayBlocked(false);
            } catch (error) {
              // Check if this is an AbortError (play interrupted by new load request)
              if (error instanceof DOMException && error.name === 'AbortError') {
                console.log('Play request was interrupted by a new load request');
                // Don't show error to user as this is expected when changing sources
              } else if (error instanceof DOMException && error.name === 'NotAllowedError') {
                console.log('Play request was blocked by browser autoplay policy');
                setAutoplayBlocked(true);
                // Don't show error to user as we'll show a play button overlay instead
              } else {
                handleError(error);
              }
            }
          })();
        });

        // Track level switching
        hls.on(Hls.Events.LEVEL_SWITCHED, (_event, data) => {
          setCurrentQuality(data.level);
        });

        // Handle errors
        hls.on(Hls.Events.ERROR, (_event, data) => {
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.error('Network error:', data);
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.error('Media error:', data);
                hls.recoverMediaError();
                break;
              default:
                console.error('Unrecoverable error:', data);

                // If this is a TS stream and we're not already using the fallback player,
                // try the direct player as a fallback
                if (stream.type === 'ts' && !usingFallbackPlayer) {
                  console.log('TS stream failed with HLS.js, trying direct player as fallback');
                  hls.destroy();
                  hlsRef.current = null;
                  setUsingFallbackPlayer(true);
                  setupDirectPlayer(streamUrl);
                  return;
                }

                hls.destroy();
                hlsRef.current = null;

                // Provide more detailed error for TS streams
                if (stream.type === 'ts') {
                  handleError(new Error(`TS stream playback error: ${data.details}. This may be due to an incompatible TS format or CORS issues.`));
                } else {
                  handleError(new Error(data.details));
                }
                break;
            }
          }
        });
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        // Native HLS support (Safari)
        video.src = url;
        video.addEventListener('loadedmetadata', () => {
          setIsLoading(false);
          // Use async/await to properly handle play() promise
          (async () => {
            try {
              await video.play();
              setIsPlaying(true);
              setAutoplayBlocked(false);
            } catch (error) {
              // Check if this is an AbortError (play interrupted by new load request)
              if (error instanceof DOMException && error.name === 'AbortError') {
                console.log('Play request was interrupted by a new load request');
                // Don't show error to user as this is expected when changing sources
              } else if (error instanceof DOMException && error.name === 'NotAllowedError') {
                console.log('Play request was blocked by browser autoplay policy');
                setAutoplayBlocked(true);
                // Don't show error to user as we'll show a play button overlay instead
              } else {
                handleError(error);
              }
            }
          })();
        });
      } else {
        handleError(new Error('HLS is not supported in this browser'));
      }
    };



    const setupDirectPlayer = (url: string) => {
      video.src = url;
      video.addEventListener('loadedmetadata', () => {
        setIsLoading(false);
        // Use async/await to properly handle play() promise
        (async () => {
          try {
            await video.play();
            setIsPlaying(true);
            setAutoplayBlocked(false);
          } catch (error) {
            // Check if this is an AbortError (play interrupted by new load request)
            if (error instanceof DOMException && error.name === 'AbortError') {
              console.log('Play request was interrupted by a new load request');
              // Don't show error to user as this is expected when changing sources
            } else if (error instanceof DOMException && error.name === 'NotAllowedError') {
              console.log('Play request was blocked by browser autoplay policy');
              setAutoplayBlocked(true);
              // Don't show error to user as we'll show a play button overlay instead
            } else {
              handleError(error);
            }
          }
        })();
      });
    };

    // Determine which URL to use
    const streamUrl = usedProxyUrl && stream.proxy_url ? stream.proxy_url : stream.url;

    // Clear any existing error
    setError(null);
    setIsLoading(true);

    // Set up the player based on stream type
    if (stream.type === 'hls' || stream.type === 'm3u8') {
      setupHlsPlayer(streamUrl);
    } else {
      setupDirectPlayer(streamUrl);
    }

    // Set up event listeners for custom controls
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', () => setIsPlaying(true));
    video.addEventListener('pause', () => setIsPlaying(false));
    video.addEventListener('volumechange', () => {
      setIsMuted(video.muted);
      setVolume(video.volume);
    });
    video.addEventListener('error', (e) => {
      if (video.error) {
        handleError(video.error);
      } else {
        handleError(e);
      }
    });

    // Set up fullscreen change event listener
    document.addEventListener('fullscreenchange', () => {
      setIsFullscreen(!!document.fullscreenElement);
    });

    // Clean up on unmount
    return () => {
      // Remove event listeners
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', () => setIsPlaying(true));
      video.removeEventListener('pause', () => setIsPlaying(false));
      video.removeEventListener('volumechange', () => {
        setIsMuted(video.muted);
        setVolume(video.volume);
      });
      video.removeEventListener('error', (e) => handleError(e));
      document.removeEventListener('fullscreenchange', () => {
        setIsFullscreen(!!document.fullscreenElement);
      });

      // Destroy HLS instance if it exists
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      // Clear any timeout
      if (controlsTimeout.current) {
        clearTimeout(controlsTimeout.current);
      }
    };
  }, [stream, usedProxyUrl, handleTimeUpdate, usingFallbackPlayer, onError]);

  // Format time for display (MM:SS)
  const formatTime = (seconds: number) => {
    if (isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle showing/hiding controls on mouse movement
  const handleMouseMove = useCallback(() => {
    setShowControls(true);

    // Clear any existing timeout
    if (controlsTimeout.current) {
      clearTimeout(controlsTimeout.current);
    }

    // Set a new timeout to hide controls after 3 seconds
    controlsTimeout.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  }, []);

  // Handle error display
  const handleError = (error: any) => {
    const errorMessage = error.message || 'Unknown error';
    setError(errorMessage);
    if (onError) onError(errorMessage);
  };

  return (
    <div
      ref={containerRef}
      data-testid="video-player"
      className={`relative overflow-hidden bg-black ${className || ''}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Video element */}
      <video
        ref={videoRef}
        data-testid="video-element"
        className="w-full h-full"
        playsInline
        muted={isMuted}
        onClick={togglePlay}
        onDoubleClick={toggleFullscreen}
      />

      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div data-testid="loading-spinner" className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 text-white p-4">
          <div className="text-center max-w-md">
            <h3 className="text-lg font-bold mb-2">Playback Error</h3>
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Autoplay blocked overlay */}
      {autoplayBlocked && !userInteracted && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
          <Button
            size="lg"
            onClick={togglePlay}
            className="flex items-center gap-2 bg-primary hover:bg-primary/90"
          >
            <Play className="h-6 w-6" />
            <span className="text-lg">Click to Play</span>
          </Button>
        </div>
      )}

      {/* Custom controls */}
      <div
        className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 transition-opacity duration-300 ${
          showControls || !isPlaying ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      >
        {/* Progress bar */}
        <div
          ref={progressRef}
          className="w-full h-2 bg-gray-700 rounded-full mb-2 cursor-pointer"
          onClick={handleProgressClick}
        >
          <div
            className="h-full bg-primary rounded-full relative"
            style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
          >
            <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-primary rounded-full"></div>
          </div>
        </div>

        {/* Time display */}
        <div className="flex items-center justify-between mb-1">
          <span className="text-white text-xs">{formatTime(currentTime)}</span>
          <span className="text-white text-xs">{formatTime(duration)}</span>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Play/Pause button */}
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={togglePlay}
              data-testid="play-pause-button"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </Button>

            {/* Skip backward button */}
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={skipBackward}
            >
              <SkipBack className="h-5 w-5" />
            </Button>

            {/* Skip forward button */}
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={skipForward}
            >
              <SkipForward className="h-5 w-5" />
            </Button>

            {/* Volume control */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/20"
                onClick={toggleMute}
                data-testid="mute-button"
              >
                {isMuted || volume === 0 ? (
                  <VolumeX className="h-5 w-5" />
                ) : (
                  <Volume2 className="h-5 w-5" />
                )}
              </Button>
              <div className="hidden sm:block w-24">
                <Slider
                  value={[isMuted ? 0 : volume]}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={handleVolumeChange}
                  className="h-1"
                  data-testid="volume-slider"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Quality selector */}
            {qualityLevels.length > 0 && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-white/20"
                  >
                    <Settings className="h-5 w-5" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-56 p-2">
                  <div className="space-y-2">
                    <h3 className="font-medium">Quality</h3>
                    <RadioGroup
                      value={currentQuality.toString()}
                      onValueChange={(value) => changeQuality(parseInt(value))}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="-1" id="auto" />
                        <Label htmlFor="auto">Auto</Label>
                      </div>
                      {qualityLevels.map((level) => (
                        <div key={level.level} className="flex items-center space-x-2">
                          <RadioGroupItem value={level.level.toString()} id={`level-${level.level}`} />
                          <Label htmlFor={`level-${level.level}`}>
                            {level.name}
                            {level.height > 0 && level.width > 0 && ` (${level.width}x${level.height})`}
                            {level.bitrate > 0 && ` - ${Math.round(level.bitrate / 1000)} Kbps`}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>

                    <div className="pt-2 border-t">
                      <h3 className="font-medium mb-1">Playback Speed</h3>
                      <div className="flex flex-wrap gap-1">
                        {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                          <Button
                            key={rate}
                            variant={playbackRate === rate ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => changePlaybackRate(rate)}
                            className="flex-1 min-w-[40px]"
                          >
                            {rate === 1 ? 'Normal' : `${rate}x`}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            )}

            {/* Fullscreen button */}
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
