import React, { ReactNode } from 'react';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  id: string;
  label: string;
  children: ReactNode;
  error?: string;
  required?: boolean;
  className?: string;
  description?: string;
}

/**
 * Form Field Component
 * 
 * Provides a consistent form field layout with:
 * - Label
 * - Input/control (passed as children)
 * - Optional error message
 * - Optional required indicator (red asterisk)
 * - Optional description
 */
export default function FormField({ 
  id, 
  label, 
  children, 
  error, 
  required = false,
  className,
  description
}: FormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between items-center">
        <Label htmlFor={id} className="flex items-center">
          {label}
          {required && <span className="ml-1 text-red-500">*</span>}
        </Label>
      </div>
      
      {description && (
        <p className="text-muted-foreground text-sm">{description}</p>
      )}
      
      {children}
      
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}
