import React from 'react';
import { cn } from '@/lib/utils';

interface RequiredFieldNoteProps {
  className?: string;
}

/**
 * Required Field Note Component
 * 
 * Displays a standardized note about required fields
 */
export default function RequiredFieldNote({ className }: RequiredFieldNoteProps) {
  return (
    <p className={cn("text-sm text-muted-foreground mt-4", className)}>
      <span className="text-red-500">*</span> Required fields
    </p>
  );
}
