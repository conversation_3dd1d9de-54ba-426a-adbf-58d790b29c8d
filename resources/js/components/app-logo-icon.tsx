import { SVGAttributes } from 'react';

export default function AppLogoIcon(props: SVGAttributes<SVGElement>) {
    return (
        <svg {...props} viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            {/* TV Body */}
            <rect x="2" y="6" width="28" height="20" rx="2" />
            {/* TV Screen */}
            <rect x="4" y="8" width="24" height="16" rx="1" fillOpacity="0.2" />
            {/* TV Stand */}
            <rect x="12" y="26" width="8" height="2" rx="1" />
            {/* Play Button */}
            <polygon points="14,16 20,12 20,20" />
        </svg>
    );
}
