import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import StreamDetailModal from './stream-detail-modal';
import { describe, expect, it, vi, beforeEach } from 'vitest';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
  Link: ({ href, children, method, as }) => (
    <a href={href} data-method={method} data-as={as}>
      {children}
    </a>
  ),
}));

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }) => (
    open ? <div data-testid="dialog">{children}</div> : null
  ),
  DialogContent: ({ children, className }) => (
    <div data-testid="dialog-content" className={className}>{children}</div>
  ),
  DialogHeader: ({ children }) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children, className }) => (
    <div data-testid="dialog-title" className={className}>{children}</div>
  ),
}));

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, defaultValue }) => (
    <div data-testid="tabs" data-default-value={defaultValue}>{children}</div>
  ),
  TabsList: ({ children, className }) => (
    <div data-testid="tabs-list" className={className}>{children}</div>
  ),
  TabsTrigger: ({ children, value }) => (
    <button data-testid={`tab-trigger-${value}`}>{children}</button>
  ),
  TabsContent: ({ children, value }) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardHeader: ({ children, className }) => (
    <div data-testid="card-header" className={className}>{children}</div>
  ),
  CardTitle: ({ children, className }) => (
    <div data-testid="card-title" className={className}>{children}</div>
  ),
  CardContent: ({ children, className }) => (
    <div data-testid="card-content" className={className}>{children}</div>
  ),
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant, className }) => (
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
  ),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, variant, size, asChild, className }) => {
    if (asChild) {
      return <div data-testid="button-wrapper" data-variant={variant} data-size={size} className={className}>{children}</div>;
    }
    return (
      <button data-testid="button" data-variant={variant} data-size={size} className={className}>
        {children}
      </button>
    );
  },
}));

vi.mock('@/components/streams/StreamIcon', () => ({
  default: ({ logoUrl, name, className }) => (
    <div data-testid="stream-icon" data-logo-url={logoUrl} data-name={name} className={className}></div>
  ),
}));

// Mock route function
global.route = vi.fn((name, params) => `/${name}${params ? '/' + params : ''}`);

// Create test fixtures
const createBasicStream = () => ({
  id: 1,
  name: 'Test Stream',
  url: 'https://example.com/stream.m3u8',
  type: 'hls',
  status: 'online',
  last_checked_at: '2023-01-01T12:00:00Z',
  tvg_id: null,
  tvg_name: null,
  tvg_logo: null,
  group_title: null,
  metadata: { test: 'data' },
});

const createStreamWithCategories = () => ({
  ...createBasicStream(),
  categories: [
    { id: 1, name: 'Category 1', slug: 'category-1' },
    { id: 2, name: 'Category 2', slug: 'category-2' },
  ],
});

const createStreamWithTags = () => ({
  ...createBasicStream(),
  tags: [
    { id: 1, name: 'Tag 1', slug: 'tag-1' },
    { id: 2, name: 'Tag 2', slug: 'tag-2' },
  ],
});

const createStreamWithPlaylists = () => ({
  ...createBasicStream(),
  playlists: [
    { id: 1, name: 'Playlist 1', is_public: true },
    { id: 2, name: 'Playlist 2', is_public: false },
  ],
});

const createStreamWithTvgData = () => ({
  ...createBasicStream(),
  tvg_id: 'test_id',
  tvg_name: 'Test Name',
  tvg_logo: 'https://example.com/logo.png',
});

describe('StreamDetailModal', () => {
  const onOpenChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders nothing when stream is null', () => {
    const { container } = render(
      <StreamDetailModal stream={null} open={true} onOpenChange={onOpenChange} />
    );
    expect(container).toBeEmptyDOMElement();
  });

  it('renders basic stream details correctly', () => {
    render(
      <StreamDetailModal
        stream={createBasicStream()}
        open={true}
        onOpenChange={onOpenChange}
      />
    );

    // Check that the dialog is rendered
    expect(screen.getByTestId('dialog')).toBeInTheDocument();

    // Check that the stream name is displayed
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('Test Stream');

    // Check that the status badge is displayed
    expect(screen.getAllByText('Online')[0]).toBeInTheDocument();

    // Check that the type badge is displayed
    expect(screen.getAllByText('HLS')[0]).toBeInTheDocument();

    // Check that the URL is displayed
    expect(screen.getByText('https://example.com/stream.m3u8')).toBeInTheDocument();

    // Check that the tabs are rendered
    expect(screen.getByTestId('tabs')).toBeInTheDocument();
    expect(screen.getByTestId('tab-trigger-info')).toBeInTheDocument();
    expect(screen.getByTestId('tab-trigger-metadata')).toBeInTheDocument();

    // Check that the info tab content is rendered
    expect(screen.getByTestId('tab-content-info')).toBeInTheDocument();

    // Check that the metadata tab content is rendered
    expect(screen.getByTestId('tab-content-metadata')).toBeInTheDocument();

    // Check for the metadata content using a more flexible approach
    const metadataContent = screen.getByTestId('tab-content-metadata');
    expect(metadataContent).toHaveTextContent('test');
    expect(metadataContent).toHaveTextContent('data');
  });

  it('renders categories tab when stream has categories', () => {
    render(
      <StreamDetailModal
        stream={createStreamWithCategories()}
        open={true}
        onOpenChange={onOpenChange}
      />
    );

    // Check that the categories tab trigger is rendered
    expect(screen.getByTestId('tab-trigger-categories')).toBeInTheDocument();

    // Check that the categories tab content is rendered
    expect(screen.getByTestId('tab-content-categories')).toBeInTheDocument();

    // Check that the category names are displayed
    expect(screen.getByText('Category 1')).toBeInTheDocument();
    expect(screen.getByText('Category 2')).toBeInTheDocument();
  });

  it('renders tags tab when stream has tags', () => {
    render(
      <StreamDetailModal
        stream={createStreamWithTags()}
        open={true}
        onOpenChange={onOpenChange}
      />
    );

    // Check that the tags tab trigger is rendered
    expect(screen.getByTestId('tab-trigger-tags')).toBeInTheDocument();

    // Check that the tags tab content is rendered
    expect(screen.getByTestId('tab-content-tags')).toBeInTheDocument();

    // Check that the tag names are displayed
    expect(screen.getByText('Tag 1')).toBeInTheDocument();
    expect(screen.getByText('Tag 2')).toBeInTheDocument();
  });

  it('renders playlists tab when stream has playlists', () => {
    render(
      <StreamDetailModal
        stream={createStreamWithPlaylists()}
        open={true}
        onOpenChange={onOpenChange}
      />
    );

    // Check that the playlists tab trigger is rendered
    expect(screen.getByTestId('tab-trigger-playlists')).toBeInTheDocument();

    // Check that the playlists tab content is rendered
    expect(screen.getByTestId('tab-content-playlists')).toBeInTheDocument();

    // Check that the playlist names are displayed
    expect(screen.getByText('Playlist 1')).toBeInTheDocument();
    expect(screen.getByText('Playlist 2')).toBeInTheDocument();

    // Check that the public status is displayed
    expect(screen.getByText('(Public)')).toBeInTheDocument();
  });

  it('does not render dialog when open is false', () => {
    render(
      <StreamDetailModal
        stream={createBasicStream()}
        open={false}
        onOpenChange={onOpenChange}
      />
    );

    // Check that the dialog is not rendered
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });
});
