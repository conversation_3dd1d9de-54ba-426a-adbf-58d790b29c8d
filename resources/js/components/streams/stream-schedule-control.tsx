import React, { useState, useEffect } from 'react';
import { useForm } from '@inertiajs/react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  Clock,
  RefreshCw,
  CheckCircle,
  CalendarClock,
  Settings2,
  Save,
  Check
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import axios from 'axios';

interface StreamSchedule {
  id: number;
  type: 'validation' | 'refetch' | 'both';
  interval_hours: number;
  is_active: boolean;
  last_run_at: string | null;
  next_run_at: string | null;
}

export default function StreamScheduleControl() {
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [schedule, setSchedule] = useState<StreamSchedule | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // We'll still use the form for state management, but not for submission
  const form = useForm({
    type: 'validation',
    interval_hours: 24,
    is_active: false,
  });

  // Fetch the current schedule on component mount
  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setLoading(true);
        const response = await axios.get(route('stream-schedules.get'));
        const { schedule } = response.data;

        if (schedule) {
          setSchedule(schedule);
          form.setData({
            type: schedule.type,
            interval_hours: schedule.interval_hours,
            is_active: schedule.is_active,
          });
        }
      } catch (error) {
        console.error('Failed to fetch schedule', error);
        toast.error('Failed to load schedule settings');
      } finally {
        setLoading(false);
      }
    };

    fetchSchedule();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Set processing state
    setProcessing(true);

    // Use axios instead of Inertia form
    axios.post(route('stream-schedules.store'), {
      type: form.data.type,
      interval_hours: form.data.interval_hours,
      is_active: form.data.is_active
    })
      .then(response => {
        const { schedule } = response.data;
        setSchedule(schedule);
        toast.success('Schedule settings saved');
        setDialogOpen(false); // Close the dialog on success
      })
      .catch(error => {
        console.error('Failed to save schedule settings', error);
        toast.error('Failed to save schedule settings');
      })
      .finally(() => {
        setProcessing(false);
      });
  };

  const handleToggle = () => {
    setProcessing(true);

    axios.post(route('stream-schedules.toggle'))
      .then(response => {
        const { schedule } = response.data;
        setSchedule(schedule);
        form.setData('is_active', schedule.is_active);
        toast.success(response.data.message);
      })
      .catch(error => {
        console.error('Failed to toggle schedule', error);
        toast.error('Failed to toggle schedule');
      })
      .finally(() => {
        setProcessing(false);
      });
  };

  const formatDateTime = (dateTimeString: string | null) => {
    if (!dateTimeString) return 'Not scheduled';

    const date = new Date(dateTimeString);
    return date.toLocaleString();
  };

  const getScheduleStatusText = () => {
    if (!schedule) return "Not configured";
    if (!schedule.is_active) return "Disabled";

    const typeText = {
      validation: "Validation",
      refetch: "Refetch",
      both: "Validation & Refetch"
    }[schedule.type];

    return `${typeText} every ${schedule.interval_hours}h`;
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <CalendarClock className="h-4 w-4" />
            <span className="hidden sm:inline">Schedule</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Automatic Maintenance</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {loading ? (
            <div className="flex justify-center py-4">
              <RefreshCw className="h-5 w-5 animate-spin" />
            </div>
          ) : (
            <>
              <div className="px-2 py-1.5 text-sm">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Status:</span>
                  {schedule && (
                    <Switch
                      checked={form.data.is_active}
                      onCheckedChange={() => handleToggle()}
                      disabled={processing}
                      className="scale-75"
                    />
                  )}
                </div>
                <div className="text-muted-foreground text-xs mt-1">
                  {getScheduleStatusText()}
                </div>
              </div>

              {schedule && schedule.is_active && (
                <>
                  <DropdownMenuSeparator />
                  <div className="px-2 py-1.5 text-sm">
                    {schedule.last_run_at && (
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Last run:</span>
                        <span>{formatDateTime(schedule.last_run_at)}</span>
                      </div>
                    )}
                    {schedule.next_run_at && (
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-muted-foreground">Next run:</span>
                        <span>{formatDateTime(schedule.next_run_at)}</span>
                      </div>
                    )}
                  </div>
                </>
              )}

              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setDialogOpen(true)}>
                <Settings2 className="h-4 w-4 mr-2" />
                Configure Schedule
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Configure Automatic Maintenance</DialogTitle>
            <DialogDescription>
              Schedule automatic validation and refetching of your streams
            </DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4 py-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Operation Type</Label>
                  <Select
                    value={form.data.type}
                    onValueChange={(value) => form.setData('type', value as 'validation' | 'refetch' | 'both')}
                    disabled={processing}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select operation type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="validation">Validation Only</SelectItem>
                      <SelectItem value="refetch">Refetch Only</SelectItem>
                      <SelectItem value="both">Both Validation & Refetch</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="interval_hours">Interval (Hours)</Label>
                  <Input
                    id="interval_hours"
                    type="number"
                    min="1"
                    max="168"
                    value={form.data.interval_hours}
                    onChange={(e) => form.setData('interval_hours', parseInt(e.target.value))}
                    disabled={processing}
                  />
                  <p className="text-xs text-muted-foreground">
                    Set how often the operation should run (1-168 hours)
                  </p>
                </div>

                <div className="flex items-center space-x-2 pt-2">
                  <Switch
                    id="is_active"
                    checked={form.data.is_active}
                    onCheckedChange={(checked: boolean) => form.setData('is_active', checked)}
                    disabled={processing}
                  />
                  <Label htmlFor="is_active">Enable automatic maintenance</Label>
                </div>
              </div>

              <DialogFooter>
                <Button
                  type="submit"
                  disabled={processing}
                  className="gap-2"
                  title="Save Schedule"
                >
                  {processing ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                    </>
                  )}

                  Save
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
