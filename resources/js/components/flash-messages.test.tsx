import { render } from '@testing-library/react';
import { usePage } from '@inertiajs/react';
import { toast } from 'sonner';
import FlashMessages from './flash-messages';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
  usePage: vi.fn(),
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe('FlashMessages', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should call toast.success when flash.success is present', () => {
    // Mock usePage to return flash.success
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {
          success: 'Success message',
        },
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.success).toHaveBeenCalledWith('Success message');
    expect(toast.error).not.toHaveBeenCalled();
    expect(toast.warning).not.toHaveBeenCalled();
    expect(toast.info).not.toHaveBeenCalled();
  });

  it('should call toast.error when flash.error is present', () => {
    // Mock usePage to return flash.error
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {
          error: 'Error message',
        },
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.error).toHaveBeenCalledWith('Error message');
    expect(toast.success).not.toHaveBeenCalled();
    expect(toast.warning).not.toHaveBeenCalled();
    expect(toast.info).not.toHaveBeenCalled();
  });

  it('should call toast.warning when flash.warning is present', () => {
    // Mock usePage to return flash.warning
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {
          warning: 'Warning message',
        },
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.warning).toHaveBeenCalledWith('Warning message');
    expect(toast.success).not.toHaveBeenCalled();
    expect(toast.error).not.toHaveBeenCalled();
    expect(toast.info).not.toHaveBeenCalled();
  });

  it('should call toast.info when flash.info is present', () => {
    // Mock usePage to return flash.info
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {
          info: 'Info message',
        },
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.info).toHaveBeenCalledWith('Info message');
    expect(toast.success).not.toHaveBeenCalled();
    expect(toast.error).not.toHaveBeenCalled();
    expect(toast.warning).not.toHaveBeenCalled();
  });

  it('should handle multiple flash messages', () => {
    // Mock usePage to return multiple flash messages
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {
          success: 'Success message',
          error: 'Error message',
          warning: 'Warning message',
          info: 'Info message',
        },
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.success).toHaveBeenCalledWith('Success message');
    expect(toast.error).toHaveBeenCalledWith('Error message');
    expect(toast.warning).toHaveBeenCalledWith('Warning message');
    expect(toast.info).toHaveBeenCalledWith('Info message');
  });

  it('should not call any toast methods when no flash messages are present', () => {
    // Mock usePage to return no flash messages
    vi.mocked(usePage).mockReturnValue({
      props: {
        flash: {},
      },
    } as any);

    render(<FlashMessages />);
    
    expect(toast.success).not.toHaveBeenCalled();
    expect(toast.error).not.toHaveBeenCalled();
    expect(toast.warning).not.toHaveBeenCalled();
    expect(toast.info).not.toHaveBeenCalled();
  });
});
