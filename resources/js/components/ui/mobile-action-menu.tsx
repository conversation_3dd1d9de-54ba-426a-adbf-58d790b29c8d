import React from 'react';
import { MoreVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

export interface ActionMenuItem {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: 'default' | 'destructive';
  disabled?: boolean;
}

export interface MobileActionMenuProps {
  items: ActionMenuItem[];
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

/**
 * Mobile-optimized Action Menu Component
 * 
 * Provides touch-friendly action menus with larger touch targets
 * and better spacing for mobile devices.
 */
export default function MobileActionMenu({
  items,
  className,
  triggerClassName,
  contentClassName,
}: MobileActionMenuProps) {
  const isMobile = useIsMobile();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={isMobile ? "icon" : "sm"}
          className={cn(
            "h-8 w-8",
            // Mobile optimizations
            isMobile && [
              "h-10 w-10", // Larger touch target
              "rounded-lg", // More rounded corners
            ],
            triggerClassName
          )}
        >
          <MoreVertical className={cn(
            "h-4 w-4",
            isMobile && "h-5 w-5" // Larger icon on mobile
          )} />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        align="end"
        className={cn(
          // Mobile optimizations
          isMobile && [
            "min-w-[200px]", // Wider menu on mobile
            "p-2", // More padding
          ],
          contentClassName
        )}
      >
        {items.map((item, index) => {
          const Icon = item.icon;
          
          return (
            <DropdownMenuItem
              key={index}
              onClick={item.onClick}
              disabled={item.disabled}
              className={cn(
                "flex items-center gap-2 cursor-pointer",
                // Mobile optimizations
                isMobile && [
                  "h-12", // Larger touch target
                  "px-4 py-3", // More generous padding
                  "text-base", // Larger text
                ],
                // Desktop styles
                !isMobile && [
                  "h-8", // Standard height
                  "px-2 py-1.5", // Standard padding
                  "text-sm", // Standard text size
                ],
                // Variant styles
                item.variant === 'destructive' && [
                  "text-destructive",
                  "focus:text-destructive",
                  "focus:bg-destructive/10",
                ],
                className
              )}
            >
              {Icon && (
                <Icon className={cn(
                  "h-4 w-4",
                  isMobile && "h-5 w-5", // Larger icon on mobile
                  item.variant === 'destructive' && "text-destructive"
                )} />
              )}
              <span>{item.label}</span>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
