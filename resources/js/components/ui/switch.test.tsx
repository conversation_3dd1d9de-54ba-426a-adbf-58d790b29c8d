import { render, screen, fireEvent } from '@testing-library/react';
import { Switch } from './switch';
import { describe, expect, it, vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import React from 'react';

describe('Switch', () => {
  it('should render unchecked by default', () => {
    render(<Switch />);
    
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveAttribute('aria-checked', 'false');
  });

  it('should render checked when checked prop is true', () => {
    render(<Switch checked />);
    
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveAttribute('aria-checked', 'true');
  });

  it('should call onCheckedChange when clicked', async () => {
    const onCheckedChange = vi.fn();
    const user = userEvent.setup();
    
    render(<Switch onCheckedChange={onCheckedChange} />);
    
    const switchElement = screen.getByRole('switch');
    await user.click(switchElement);
    
    expect(onCheckedChange).toHaveBeenCalledWith(true);
  });

  it('should toggle checked state when clicked', async () => {
    const user = userEvent.setup();
    
    const { rerender } = render(<Switch checked={false} />);
    
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveAttribute('aria-checked', 'false');
    
    await user.click(switchElement);
    
    // Rerender with new checked state
    rerender(<Switch checked={true} />);
    
    expect(switchElement).toHaveAttribute('aria-checked', 'true');
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Switch disabled />);
    
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toBeDisabled();
  });

  it('should not call onCheckedChange when disabled', async () => {
    const onCheckedChange = vi.fn();
    const user = userEvent.setup();
    
    render(<Switch disabled onCheckedChange={onCheckedChange} />);
    
    const switchElement = screen.getByRole('switch');
    await user.click(switchElement);
    
    expect(onCheckedChange).not.toHaveBeenCalled();
  });

  it('should apply custom className', () => {
    render(<Switch className="custom-class" />);
    
    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveClass('custom-class');
  });
});
