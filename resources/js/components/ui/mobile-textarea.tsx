import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

export interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  autoOptimize?: boolean; // Automatically optimize for mobile
}

/**
 * Mobile-optimized Textarea Component
 * 
 * Automatically adjusts textarea sizes and touch targets for mobile devices
 * while maintaining desktop appearance on larger screens.
 */
const MobileTextarea = React.forwardRef<HTMLTextAreaElement, MobileTextareaProps>(
  ({ className, autoOptimize = true, ...props }, ref) => {
    const isMobile = useIsMobile();

    // Mobile-specific styles
    const mobileStyles = autoOptimize && isMobile ? {
      fontSize: "16px", // Prevents zoom on iOS
      padding: "12px 16px", // More generous padding
      minHeight: "100px", // Larger minimum height for mobile
    } : {};

    return (
      <textarea
        data-slot="textarea"
        style={mobileStyles}
        className={cn(
          "border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          // Mobile-specific classes
          autoOptimize && isMobile && [
            "min-h-[100px]", // Larger minimum height for mobile
            "text-base", // Consistent text size to prevent zoom
            "px-4 py-3", // More generous padding
          ],
          // Desktop classes
          autoOptimize && !isMobile && [
            "min-h-[80px]", // Standard desktop minimum height
            "text-sm", // Smaller text on desktop
          ],
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

MobileTextarea.displayName = "MobileTextarea";

export { MobileTextarea };
