import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

export interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  autoOptimize?: boolean; // Automatically optimize for mobile
}

/**
 * Mobile-optimized Input Component
 * 
 * Automatically adjusts input sizes and touch targets for mobile devices
 * while maintaining desktop appearance on larger screens.
 */
const MobileInput = React.forwardRef<HTMLInputElement, MobileInputProps>(
  ({ className, type, autoOptimize = true, ...props }, ref) => {
    const isMobile = useIsMobile();

    // Mobile-specific styles
    const mobileStyles = autoOptimize && isMobile ? {
      // Larger touch targets
      minHeight: "44px", // Apple's recommended minimum
      fontSize: "16px", // Prevents zoom on iOS
      padding: "12px 16px", // More generous padding
    } : {};

    return (
      <input
        type={type}
        data-slot="input"
        style={mobileStyles}
        className={cn(
          "border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
          "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
          // Mobile-specific classes
          autoOptimize && isMobile && [
            "h-11", // Larger height for mobile
            "text-base", // Consistent text size to prevent zoom
            "px-4 py-3", // More generous padding
          ],
          // Desktop classes
          autoOptimize && !isMobile && [
            "h-9", // Standard desktop height
            "md:text-sm", // Smaller text on desktop
          ],
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

MobileInput.displayName = "MobileInput";

export { MobileInput };
