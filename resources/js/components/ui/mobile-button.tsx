import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

const mobileButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 active:bg-primary/80",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 active:bg-destructive/80 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80 active:bg-secondary/70",
        ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        link: "text-primary underline-offset-4 hover:underline active:underline",
      },
      size: {
        default: "h-10 px-4 py-2 has-[>svg]:px-3", // Increased from h-9 for better touch
        sm: "h-9 rounded-md px-3 has-[>svg]:px-2.5", // Increased from h-8
        lg: "h-12 rounded-md px-6 has-[>svg]:px-4", // Increased from h-10
        icon: "size-10", // Increased from size-9 for better touch targets
        "icon-sm": "size-9", // New smaller icon size
        "icon-lg": "size-12", // New larger icon size
      },
      touchOptimized: {
        true: "min-h-[44px] min-w-[44px]", // Apple's recommended minimum touch target
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      touchOptimized: false,
    },
  }
);

export interface MobileButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof mobileButtonVariants> {
  asChild?: boolean;
  autoOptimize?: boolean; // Automatically optimize for mobile
}

/**
 * Mobile-optimized Button Component
 * 
 * Automatically adjusts button sizes and touch targets for mobile devices
 * while maintaining desktop appearance on larger screens.
 */
const MobileButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  ({ className, variant, size, touchOptimized, autoOptimize = true, asChild = false, ...props }, ref) => {
    const isMobile = useIsMobile();
    const Comp = asChild ? Slot : "button";

    // Auto-optimize for mobile if enabled
    const shouldOptimize = autoOptimize ? isMobile : touchOptimized;

    return (
      <Comp
        className={cn(
          mobileButtonVariants({ 
            variant, 
            size, 
            touchOptimized: shouldOptimize,
            className 
          })
        )}
        ref={ref}
        {...props}
      />
    );
  }
);

MobileButton.displayName = "MobileButton";

export { MobileButton, mobileButtonVariants };
