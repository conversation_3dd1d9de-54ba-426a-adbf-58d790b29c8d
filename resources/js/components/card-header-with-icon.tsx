import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CardHeaderWithIconProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  required?: boolean;
  className?: string;
}

/**
 * Card Header With Icon Component
 * 
 * Provides a consistent card header with:
 * - Title
 * - Optional description
 * - Optional icon
 * - Optional required indicator (red asterisk)
 */
export default function CardHeaderWithIcon({ 
  title, 
  description, 
  icon: Icon, 
  required = false,
  className 
}: CardHeaderWithIconProps) {
  return (
    <CardHeader className={cn("pb-3", className)}>
      <div className="flex items-center space-x-2">
        {Icon && <Icon className="h-5 w-5" />}
        <div>
          <CardTitle className="flex items-center">
            {title}
            {required && <span className="ml-1 text-red-500">*</span>}
          </CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
      </div>
    </CardHeader>
  );
}
