import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Download,
  RefreshCw,
  Trash2,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileDown,
  Github,
} from 'lucide-react';

interface ExportJob {
  id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path: string | null;
  created_at: string;
  expires_at: string;
  metadata?: {
    github_export?: boolean;
    github_repo?: string;
    github_path?: string;
    github_note?: string;
    github_result?: {
      success: boolean;
      error?: string;
      no_changes?: boolean;
    };
  };
}

interface ExportJobsListProps {
  exports: ExportJob[];
  onExportDeleted: () => void;
}

const ExportJobsList: React.FC<ExportJobsListProps> = ({ exports, onExportDeleted }) => {
  // State for delete confirmation
  const [exportToDelete, setExportToDelete] = useState<ExportJob | null>(null);
  
  // Delete an export
  const deleteExport = async () => {
    if (!exportToDelete) return;
    
    try {
      await axios.delete(route('export.delete', exportToDelete.id));
      toast.success('Export deleted successfully');
      setExportToDelete(null);
      onExportDeleted();
    } catch (error) {
      console.error('Error deleting export:', error);
      toast.error('Failed to delete export');
    }
  };
  
  // Get status badge
  const getExportStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </Badge>
        );
      case 'processing':
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <RefreshCw className="h-3 w-3 animate-spin" />
            Processing
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="success" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Completed
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <XCircle className="h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {status}
          </Badge>
        );
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Exports</CardTitle>
        <CardDescription>
          Your recent export jobs. Exports are automatically deleted after 3 days.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {exports.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            No exports found. Generate an export to see it here.
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {exports.map(exportJob => (
                  <TableRow key={exportJob.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        {exportJob.metadata?.github_export && (
                          <Github className="h-4 w-4 text-muted-foreground" />
                        )}
                        {exportJob.name}
                      </div>
                      {exportJob.metadata?.github_repo && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {exportJob.metadata.github_repo}:{exportJob.metadata.github_path}
                        </div>
                      )}
                      {exportJob.status === 'failed' && exportJob.metadata?.github_result?.error && (
                        <div className="text-xs text-destructive mt-1">
                          Error: {exportJob.metadata.github_result.error}
                        </div>
                      )}
                      {exportJob.status === 'completed' && exportJob.metadata?.github_result?.no_changes && (
                        <div className="text-xs text-muted-foreground mt-1">
                          No changes were made (content identical)
                        </div>
                      )}
                    </TableCell>
                    <TableCell>{getExportStatusBadge(exportJob.status)}</TableCell>
                    <TableCell>{formatDistanceToNow(new Date(exportJob.created_at), { addSuffix: true })}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        {exportJob.status === 'completed' && (
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <a
                              href={route('export.download', exportJob.id)}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center"
                            >
                              <FileDown className="h-4 w-4 mr-1" />
                              Download
                            </a>
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setExportToDelete(exportJob)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        
        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!exportToDelete} onOpenChange={(open) => !open && setExportToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete the export job and any associated files.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={deleteExport}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default ExportJobsList;
