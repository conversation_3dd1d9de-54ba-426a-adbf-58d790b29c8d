import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TableActionButtonProps {
  icon: LucideIcon;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  method?: 'get' | 'post' | 'put' | 'patch' | 'delete';
  disabled?: boolean;
  className?: string;
  title: string;
  color?: string;
}

/**
 * Table Action Button Component
 * 
 * Provides a consistent icon button for table row actions with:
 * - Icon
 * - Link or onClick functionality
 * - Tooltip title
 * - Consistent styling
 */
export default function TableActionButton({ 
  icon: Icon, 
  href, 
  onClick, 
  variant = 'ghost',
  method = 'get',
  disabled = false,
  className,
  title,
  color
}: TableActionButtonProps) {
  const buttonClassName = cn(
    "h-8 w-8",
    color && `text-${color} hover:text-${color}/90`,
    className
  );
  
  // If href is provided, render as a Link
  if (href) {
    return (
      <Button
        variant={variant}
        size="icon"
        disabled={disabled}
        className={buttonClassName}
        title={title}
        asChild
      >
        <Link
          href={href}
          method={method}
          as={method !== 'get' ? 'button' : undefined}
        >
          <Icon className="h-4 w-4" />
        </Link>
      </Button>
    );
  }
  
  // Otherwise, render as a regular button
  return (
    <Button
      variant={variant}
      size="icon"
      onClick={onClick}
      disabled={disabled}
      className={buttonClassName}
      title={title}
    >
      <Icon className="h-4 w-4" />
    </Button>
  );
}
