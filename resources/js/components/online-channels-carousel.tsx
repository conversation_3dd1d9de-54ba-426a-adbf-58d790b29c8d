import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import StreamIcon from '@/components/streams/stream-icon';
import { Play } from 'lucide-react';

interface Stream {
  id: number;
  name: string;
  type: string;
  status: string;
  tvg_logo: string | null;
}

interface OnlineChannelsCarouselProps {
  onSelectStream: (streamId: number) => void;
  currentStreamId: number;
}

const OnlineChannelsCarousel: React.FC<OnlineChannelsCarouselProps> = ({ 
  onSelectStream,
  currentStreamId
}) => {
  const [streams, setStreams] = useState<Stream[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOnlineStreams = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await axios.get(route('api.streams.online'));
        setStreams(response.data);
      } catch (err) {
        console.error('Error fetching online streams:', err);
        setError('Failed to load online channels');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOnlineStreams();
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="text-center text-red-500">{error}</div>
        </CardContent>
      </Card>
    );
  }

  if (streams.length === 0) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="text-center text-muted-foreground">No online channels available</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <h3 className="text-lg font-medium mb-3">Online Channels</h3>
        <ScrollArea className="w-full whitespace-nowrap">
          <div className="flex space-x-4 pb-2">
            {streams.map((stream) => (
              <div 
                key={stream.id} 
                className={`flex-none w-32 cursor-pointer transition-all duration-200 ${
                  stream.id === currentStreamId ? 'scale-105 ring-2 ring-primary rounded-md' : 'hover:scale-105'
                }`}
                onClick={() => onSelectStream(stream.id)}
              >
                <div className="relative aspect-video bg-muted rounded-md overflow-hidden mb-2">
                  <StreamIcon 
                    logoUrl={stream.tvg_logo} 
                    name={stream.name} 
                    className="w-full h-full object-contain"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity">
                    <Play className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div className="truncate text-sm font-medium">{stream.name}</div>
                <div className="text-xs text-muted-foreground">{stream.type.toUpperCase()}</div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default OnlineChannelsCarousel;
