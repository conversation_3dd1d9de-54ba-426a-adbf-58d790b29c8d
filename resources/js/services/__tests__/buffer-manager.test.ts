import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import BufferManager from '../buffer-manager';

describe('BufferManager', () => {
  let bufferManager: BufferManager;

  beforeEach(() => {
    bufferManager = new BufferManager('balanced');
  });

  afterEach(() => {
    bufferManager.destroy();
  });

  it('should initialize with balanced strategy', () => {
    const strategy = bufferManager.getStrategy();
    expect(strategy.name).toBe('Balanced');
    expect(strategy.maxBufferLength).toBe(30);
    expect(strategy.maxMaxBufferLength).toBe(60);
  });

  it('should provide HLS configuration', () => {
    const config = bufferManager.getHlsConfig();
    expect(config.maxBufferLength).toBe(30);
    expect(config.maxMaxBufferLength).toBe(60);
    expect(config.bufferWatermark).toBe(10);
  });

  it('should adjust configuration for TS streams', () => {
    const config = bufferManager.getHlsConfig('ts');
    expect(config.manifestLoadingTimeOut).toBeGreaterThan(10000);
    expect(config.fragLoadingTimeOut).toBeGreaterThan(20000);
  });

  it('should change strategy', () => {
    bufferManager.setStrategy('conservative');
    const strategy = bufferManager.getStrategy();
    expect(strategy.name).toBe('Conservative');
    expect(strategy.maxBufferLength).toBe(60);
  });

  it('should record stalling events', () => {
    bufferManager.recordStalling(1000);
    const health = bufferManager.getBufferHealth();
    expect(health.stallingEvents).toBe(1);
    expect(health.rebufferingTime).toBe(1000);
  });

  it('should update network conditions', () => {
    bufferManager.updateNetworkCondition(5, 100);
    const condition = bufferManager.getNetworkCondition();
    expect(condition.bandwidth).toBe(5);
    expect(condition.latency).toBe(100);
  });

  it('should adapt to poor network conditions', () => {
    // Simulate poor network with multiple stalling events
    for (let i = 0; i < 5; i++) {
      bufferManager.recordStalling(500);
    }
    
    bufferManager.updateNetworkCondition(1, 500);
    
    // Should adapt to unstable network strategy
    const strategy = bufferManager.getStrategy();
    expect(strategy.maxBufferLength).toBeGreaterThan(30);
  });

  it('should update buffer metrics', () => {
    bufferManager.updateMetrics({
      bufferLength: 15,
      bufferEnd: 20,
      stallingCount: 1,
    });

    const metrics = bufferManager.getMetrics();
    expect(metrics.bufferLength).toBe(15);
    expect(metrics.bufferEnd).toBe(20);
    expect(metrics.stallingCount).toBe(1);
  });

  it('should provide available strategies', () => {
    const strategies = BufferManager.getAvailableStrategies();
    expect(strategies).toContain('balanced');
    expect(strategies).toContain('conservative');
    expect(strategies).toContain('aggressive');
    expect(strategies).toContain('lowLatency');
    expect(strategies).toContain('unstableNetwork');
  });

  it('should calculate network stability', () => {
    // Simulate stable network
    bufferManager.updateNetworkCondition(10, 50);
    let condition = bufferManager.getNetworkCondition();
    expect(condition.stability).toBe('stable');

    // Simulate unstable network with varying bandwidth
    const bandwidths = [10, 5, 15, 3, 12, 8, 20, 2];
    bandwidths.forEach(bw => bufferManager.updateNetworkCondition(bw, 100));
    
    condition = bufferManager.getNetworkCondition();
    expect(['unstable', 'poor']).toContain(condition.stability);
  });

  it('should handle buffer health calculations', () => {
    bufferManager.updateMetrics({
      bufferLength: 20,
    });

    const health = bufferManager.getBufferHealth();
    expect(health.currentBuffer).toBe(20);
    expect(health.bufferRatio).toBeCloseTo(20 / 30); // 20 / target buffer (30)
  });

  it('should detect buffer underruns', () => {
    // Set buffer below watermark
    bufferManager.updateMetrics({
      bufferLength: 5, // Below watermark of 10
    });

    const health = bufferManager.getBufferHealth();
    expect(health.bufferUnderruns).toBeGreaterThan(0);
  });
});
