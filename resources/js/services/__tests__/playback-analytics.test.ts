import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import PlaybackAnalytics from '../playback-analytics';

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'test-user-agent',
  },
  writable: true,
});

// Mock screen
Object.defineProperty(global, 'screen', {
  value: {
    width: 1920,
    height: 1080,
  },
  writable: true,
});

describe('PlaybackAnalytics', () => {
  let analytics: PlaybackAnalytics;

  beforeEach(() => {
    vi.useFakeTimers();
    analytics = new PlaybackAnalytics(123, 'Test Stream', 'hls');
  });

  afterEach(() => {
    analytics.destroy();
    vi.useRealTimers();
  });

  it('should initialize with stream information', () => {
    const session = analytics.getSession();
    expect(session.streamId).toBe(123);
    expect(session.streamName).toBe('Test Stream');
    expect(session.streamType).toBe('hls');
    expect(session.userAgent).toBe('test-user-agent');
    expect(session.resolution).toBe('1920x1080');
  });

  it('should record quality metrics', () => {
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    
    const qualityMetrics = analytics.getQualityMetrics();
    expect(qualityMetrics.current).toBeTruthy();
    expect(qualityMetrics.current?.bitrate).toBe(1000000);
    expect(qualityMetrics.current?.resolution).toBe('1920x1080');
    expect(qualityMetrics.current?.bufferLevel).toBe(15);
  });

  it('should track quality changes', () => {
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordQualityMetric(500000, '1280x720', 12, 0, 500000);
    
    const session = analytics.getSession();
    expect(session.qualityChanges).toBe(1);
  });

  it('should record buffering events', () => {
    analytics.recordBufferingStart('rebuffering');

    // Fast forward time by 2 seconds
    vi.advanceTimersByTime(2000);

    analytics.recordBufferingEnd();

    const bufferingStats = analytics.getBufferingStats();
    expect(bufferingStats.totalEvents).toBe(1);
  });

  it('should record errors', () => {
    analytics.recordError('network', 'NETWORK_ERROR', 'Connection failed', true, false);
    
    const errorStats = analytics.getErrorStats();
    expect(errorStats.totalErrors).toBe(1);
    expect(errorStats.fatalErrors).toBe(1);
    expect(errorStats.errorsByType.network).toBe(1);
  });

  it('should record user interactions', () => {
    analytics.recordUserInteraction('play');
    analytics.recordUserInteraction('pause');
    analytics.recordUserInteraction('volume_change', 0.8);
    
    const interactions = analytics.getUserInteractionStats();
    expect(interactions.play).toBe(1);
    expect(interactions.pause).toBe(1);
    expect(interactions.volume_change).toBe(1);
  });

  it('should update watch time', () => {
    analytics.updateWatchTime(120, 300);
    
    const session = analytics.getSession();
    expect(session.totalWatchTime).toBe(120);
    expect(session.duration).toBe(300);
  });

  it('should calculate average bitrate', () => {
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordQualityMetric(800000, '1920x1080', 12, 0, 800000);
    analytics.recordQualityMetric(1200000, '1920x1080', 18, 0, 1200000);
    
    const session = analytics.getSession();
    expect(session.averageBitrate).toBe(1000000); // (1000000 + 800000 + 1200000) / 3
  });

  it('should track peak bitrate', () => {
    analytics.recordQualityMetric(800000, '1280x720', 15, 0, 800000);
    analytics.recordQualityMetric(1500000, '1920x1080', 12, 0, 1500000);
    analytics.recordQualityMetric(1000000, '1920x1080', 18, 0, 1000000);
    
    const session = analytics.getSession();
    expect(session.peakBitrate).toBe(1500000);
  });

  it('should calculate quality stability', () => {
    // Stable quality (same bitrate)
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    
    const qualityMetrics = analytics.getQualityMetrics();
    expect(qualityMetrics.stability).toBeGreaterThan(90);
  });

  it('should calculate buffering ratio', () => {
    analytics.updateWatchTime(100, 300); // 100 seconds watched
    analytics.recordBufferingStart('rebuffering');

    // Simulate 5 seconds of buffering
    vi.advanceTimersByTime(5000);
    analytics.recordBufferingEnd();

    const bufferingStats = analytics.getBufferingStats();
    // The buffering ratio should be total buffering time (ms) / total watch time (seconds)
    // 5000ms / 100s = 50
    expect(bufferingStats.bufferingRatio).toBeCloseTo(50); // 5000ms / 100s = 50
  });

  it('should end session and provide final report', () => {
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordUserInteraction('play');
    analytics.updateWatchTime(60, 120);
    
    const finalSession = analytics.endSession();
    expect(finalSession.endTime).toBeTruthy();
    expect(finalSession.totalWatchTime).toBe(60);
  });

  it('should export complete analytics data', () => {
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    analytics.recordError('network', 'TIMEOUT', 'Request timeout', false, true);
    analytics.recordUserInteraction('play');
    
    const exportData = analytics.exportData();
    expect(exportData.session).toBeTruthy();
    expect(exportData.qualityMetrics).toHaveLength(1);
    expect(exportData.errorEvents).toHaveLength(1);
    expect(exportData.userInteractions).toHaveLength(1);
  });

  it('should handle multiple buffering events', () => {
    // First buffering event
    analytics.recordBufferingStart('initial');
    vi.advanceTimersByTime(2000);
    analytics.recordBufferingEnd();
    
    // Second buffering event
    analytics.recordBufferingStart('rebuffering');
    vi.advanceTimersByTime(1000);
    analytics.recordBufferingEnd();
    
    const bufferingStats = analytics.getBufferingStats();
    expect(bufferingStats.totalEvents).toBe(2);
    expect(bufferingStats.totalTime).toBe(3000);
    expect(bufferingStats.averageDuration).toBe(1500);
  });

  it('should clean up old metrics', () => {
    // Record metrics and advance time beyond retention period
    analytics.recordQualityMetric(1000000, '1920x1080', 15, 0, 1000000);
    
    // Advance time by 11 minutes (beyond 10 minute retention)
    vi.advanceTimersByTime(11 * 60 * 1000);
    
    // Record new metric to trigger cleanup
    analytics.recordQualityMetric(800000, '1280x720', 12, 0, 800000);
    
    const qualityMetrics = analytics.getQualityMetrics();
    expect(qualityMetrics.current?.bitrate).toBe(800000);
  });
});
