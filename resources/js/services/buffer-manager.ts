/**
 * Advanced Buffer Management Service
 * 
 * Provides adaptive buffering strategies for different network conditions
 * and stream types to optimize playback experience.
 */

export interface BufferStrategy {
  name: string;
  maxBufferLength: number;
  maxMaxBufferLength: number;
  bufferWatermark: number;
  lowLatencyMode: boolean;
  adaptiveBuffering: boolean;
}

export interface NetworkCondition {
  bandwidth: number; // Mbps
  latency: number; // ms
  packetLoss: number; // percentage
  stability: 'stable' | 'unstable' | 'poor';
}

export interface BufferHealth {
  currentBuffer: number;
  targetBuffer: number;
  bufferRatio: number;
  stallingEvents: number;
  rebufferingTime: number;
  averageBufferLevel: number;
  bufferUnderruns: number;
}

export interface BufferMetrics {
  bufferLength: number;
  bufferEnd: number;
  stallingCount: number;
  rebufferingDuration: number;
  averageBandwidth: number;
  droppedFrames: number;
  lastStallingTime: number;
}

export class BufferManager {
  private strategy: BufferStrategy;
  private networkCondition: NetworkCondition;
  private bufferHealth: BufferHealth;
  private metrics: BufferMetrics;
  private stallingHistory: number[] = [];
  private bandwidthHistory: number[] = [];
  private bufferLevelHistory: number[] = [];
  private adaptationTimer: NodeJS.Timeout | null = null;

  // Predefined buffer strategies
  private static readonly STRATEGIES: Record<string, BufferStrategy> = {
    conservative: {
      name: 'Conservative',
      maxBufferLength: 60,
      maxMaxBufferLength: 120,
      bufferWatermark: 15,
      lowLatencyMode: false,
      adaptiveBuffering: true,
    },
    balanced: {
      name: 'Balanced',
      maxBufferLength: 30,
      maxMaxBufferLength: 60,
      bufferWatermark: 10,
      lowLatencyMode: false,
      adaptiveBuffering: true,
    },
    aggressive: {
      name: 'Aggressive',
      maxBufferLength: 15,
      maxMaxBufferLength: 30,
      bufferWatermark: 5,
      lowLatencyMode: true,
      adaptiveBuffering: true,
    },
    lowLatency: {
      name: 'Low Latency',
      maxBufferLength: 8,
      maxMaxBufferLength: 15,
      bufferWatermark: 3,
      lowLatencyMode: true,
      adaptiveBuffering: false,
    },
    unstableNetwork: {
      name: 'Unstable Network',
      maxBufferLength: 90,
      maxMaxBufferLength: 180,
      bufferWatermark: 30,
      lowLatencyMode: false,
      adaptiveBuffering: true,
    },
  };

  constructor(initialStrategy: string = 'balanced') {
    this.strategy = BufferManager.STRATEGIES[initialStrategy] || BufferManager.STRATEGIES.balanced;
    this.networkCondition = {
      bandwidth: 0,
      latency: 0,
      packetLoss: 0,
      stability: 'stable',
    };
    this.bufferHealth = {
      currentBuffer: 0,
      targetBuffer: this.strategy.maxBufferLength,
      bufferRatio: 0,
      stallingEvents: 0,
      rebufferingTime: 0,
      averageBufferLevel: 0,
      bufferUnderruns: 0,
    };
    this.metrics = {
      bufferLength: 0,
      bufferEnd: 0,
      stallingCount: 0,
      rebufferingDuration: 0,
      averageBandwidth: 0,
      droppedFrames: 0,
      lastStallingTime: 0,
    };

    this.startAdaptiveMonitoring();
  }

  /**
   * Get HLS.js configuration based on current strategy
   */
  getHlsConfig(streamType?: string): any {
    const baseConfig = {
      maxBufferLength: this.strategy.maxBufferLength,
      maxMaxBufferLength: this.strategy.maxMaxBufferLength,
      bufferWatermark: this.strategy.bufferWatermark,
      lowLatencyMode: this.strategy.lowLatencyMode,
      
      // Fragment loading configuration
      fragLoadingTimeOut: this.getFragmentTimeout(),
      fragLoadingMaxRetry: this.getMaxRetries(),
      fragLoadingRetryDelay: this.getRetryDelay(),
      
      // Manifest loading configuration
      manifestLoadingTimeOut: this.getManifestTimeout(),
      manifestLoadingMaxRetry: 3,
      
      // Level loading configuration
      levelLoadingTimeOut: this.getLevelTimeout(),
      levelLoadingMaxRetry: this.getMaxRetries(),
      
      // ABR (Adaptive Bitrate) configuration
      abrEwmaFastLive: this.strategy.lowLatencyMode ? 5.0 : 3.0,
      abrEwmaSlowLive: this.strategy.lowLatencyMode ? 9.0 : 9.0,
      abrEwmaFastVoD: this.strategy.lowLatencyMode ? 4.0 : 3.0,
      abrEwmaSlowVoD: this.strategy.lowLatencyMode ? 15.0 : 9.0,
      
      // Buffer control
      maxBufferHole: 0.5,
      maxSeekHole: 2,
      
      // Start level
      startLevel: -1, // Auto
      capLevelToPlayerSize: false,
    };

    // Adjust for specific stream types
    if (streamType === 'ts') {
      return {
        ...baseConfig,
        manifestLoadingTimeOut: baseConfig.manifestLoadingTimeOut * 1.5,
        manifestLoadingMaxRetry: 5,
        fragLoadingTimeOut: baseConfig.fragLoadingTimeOut * 1.5,
        fragLoadingMaxRetry: 5,
      };
    }

    return baseConfig;
  }

  /**
   * Update buffer metrics from HLS.js events
   */
  updateMetrics(metrics: Partial<BufferMetrics>): void {
    this.metrics = { ...this.metrics, ...metrics };
    this.updateBufferHealth();
    
    if (this.strategy.adaptiveBuffering) {
      this.adaptStrategy();
    }
  }

  /**
   * Record a stalling event
   */
  recordStalling(duration: number): void {
    const now = Date.now();
    this.stallingHistory.push(now);
    this.metrics.stallingCount++;
    this.metrics.rebufferingDuration += duration;
    this.metrics.lastStallingTime = now;
    this.bufferHealth.stallingEvents++;
    this.bufferHealth.rebufferingTime += duration;

    // Keep only recent stalling events (last 5 minutes)
    const fiveMinutesAgo = now - 5 * 60 * 1000;
    this.stallingHistory = this.stallingHistory.filter(time => time > fiveMinutesAgo);

    // Adapt strategy if too many stalling events
    if (this.stallingHistory.length > 3) {
      this.adaptToUnstableNetwork();
    }
  }

  /**
   * Update network condition assessment
   */
  updateNetworkCondition(bandwidth: number, latency: number): void {
    this.networkCondition.bandwidth = bandwidth;
    this.networkCondition.latency = latency;
    
    // Add to bandwidth history
    this.bandwidthHistory.push(bandwidth);
    if (this.bandwidthHistory.length > 10) {
      this.bandwidthHistory.shift();
    }

    // Calculate bandwidth stability
    const bandwidthVariance = this.calculateVariance(this.bandwidthHistory);
    const stallingRate = this.stallingHistory.length / 5; // per minute

    if (stallingRate > 2 || bandwidthVariance > 0.5) {
      this.networkCondition.stability = 'poor';
    } else if (stallingRate > 1 || bandwidthVariance > 0.3) {
      this.networkCondition.stability = 'unstable';
    } else {
      this.networkCondition.stability = 'stable';
    }

    this.metrics.averageBandwidth = this.bandwidthHistory.reduce((a, b) => a + b, 0) / this.bandwidthHistory.length;
  }

  /**
   * Get current buffer strategy
   */
  getStrategy(): BufferStrategy {
    return { ...this.strategy };
  }

  /**
   * Get current buffer health
   */
  getBufferHealth(): BufferHealth {
    return { ...this.bufferHealth };
  }

  /**
   * Get current metrics
   */
  getMetrics(): BufferMetrics {
    return { ...this.metrics };
  }

  /**
   * Get network condition
   */
  getNetworkCondition(): NetworkCondition {
    return { ...this.networkCondition };
  }

  /**
   * Manually set buffer strategy
   */
  setStrategy(strategyName: string): void {
    if (BufferManager.STRATEGIES[strategyName]) {
      this.strategy = BufferManager.STRATEGIES[strategyName];
      this.bufferHealth.targetBuffer = this.strategy.maxBufferLength;
    }
  }

  /**
   * Get available strategies
   */
  static getAvailableStrategies(): string[] {
    return Object.keys(BufferManager.STRATEGIES);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.adaptationTimer) {
      clearInterval(this.adaptationTimer);
      this.adaptationTimer = null;
    }
  }

  // Private methods

  private getFragmentTimeout(): number {
    switch (this.networkCondition.stability) {
      case 'poor':
        return 30000;
      case 'unstable':
        return 25000;
      default:
        return 20000;
    }
  }

  private getMaxRetries(): number {
    return this.networkCondition.stability === 'poor' ? 5 : 3;
  }

  private getRetryDelay(): number {
    return this.networkCondition.stability === 'poor' ? 2000 : 1000;
  }

  private getManifestTimeout(): number {
    return this.networkCondition.stability === 'poor' ? 15000 : 10000;
  }

  private getLevelTimeout(): number {
    return this.networkCondition.stability === 'poor' ? 15000 : 10000;
  }

  private updateBufferHealth(): void {
    this.bufferHealth.currentBuffer = this.metrics.bufferLength;
    this.bufferHealth.bufferRatio = this.bufferHealth.currentBuffer / this.bufferHealth.targetBuffer;
    
    // Add to buffer level history
    this.bufferLevelHistory.push(this.bufferHealth.currentBuffer);
    if (this.bufferLevelHistory.length > 20) {
      this.bufferLevelHistory.shift();
    }
    
    this.bufferHealth.averageBufferLevel = 
      this.bufferLevelHistory.reduce((a, b) => a + b, 0) / this.bufferLevelHistory.length;

    // Detect buffer underruns
    if (this.bufferHealth.currentBuffer < this.strategy.bufferWatermark) {
      this.bufferHealth.bufferUnderruns++;
    }
  }

  private adaptStrategy(): void {
    const { stability } = this.networkCondition;
    const { bufferRatio, stallingEvents } = this.bufferHealth;

    // Don't adapt too frequently
    const now = Date.now();
    if (this.metrics.lastStallingTime && (now - this.metrics.lastStallingTime) < 10000) {
      return;
    }

    if (stability === 'poor' || stallingEvents > 5) {
      this.setStrategy('unstableNetwork');
    } else if (stability === 'unstable' || stallingEvents > 2) {
      this.setStrategy('conservative');
    } else if (stability === 'stable' && stallingEvents === 0 && bufferRatio > 0.8) {
      this.setStrategy('balanced');
    }
  }

  private adaptToUnstableNetwork(): void {
    if (this.strategy.name !== 'Unstable Network') {
      console.log('BufferManager: Adapting to unstable network conditions');
      this.setStrategy('unstableNetwork');
    }
  }

  private calculateVariance(values: number[]): number {
    if (values.length < 2) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    
    return Math.sqrt(variance) / mean; // Coefficient of variation
  }

  private startAdaptiveMonitoring(): void {
    // Monitor buffer health every 5 seconds
    this.adaptationTimer = setInterval(() => {
      if (this.strategy.adaptiveBuffering) {
        this.adaptStrategy();
      }
    }, 5000);
  }
}

export default BufferManager;
