/**
 * Playback Analytics Service
 * 
 * Tracks stream quality metrics, user behavior, and playback statistics
 * for performance monitoring and user experience optimization.
 */

export interface PlaybackSession {
  sessionId: string;
  streamId: number;
  streamName: string;
  streamType: string;
  startTime: number;
  endTime?: number;
  duration: number;
  totalWatchTime: number;
  bufferingTime: number;
  stallingEvents: number;
  qualityChanges: number;
  averageBitrate: number;
  peakBitrate: number;
  droppedFrames: number;
  errorCount: number;
  userAgent: string;
  resolution: string;
  networkType?: string;
}

export interface QualityMetric {
  timestamp: number;
  bitrate: number;
  resolution: string;
  bufferLevel: number;
  droppedFrames: number;
  bandwidth: number;
}

export interface BufferingEvent {
  timestamp: number;
  duration: number;
  bufferLevel: number;
  reason: 'initial' | 'rebuffering' | 'seeking' | 'quality_change';
}

export interface ErrorEvent {
  timestamp: number;
  type: 'network' | 'media' | 'other';
  code: string;
  message: string;
  fatal: boolean;
  recovered: boolean;
}

export interface UserInteraction {
  timestamp: number;
  action: 'play' | 'pause' | 'seek' | 'volume_change' | 'quality_change' | 'fullscreen' | 'speed_change';
  value?: number | string;
}

export class PlaybackAnalytics {
  private session: PlaybackSession;
  private qualityMetrics: QualityMetric[] = [];
  private bufferingEvents: BufferingEvent[] = [];
  private errorEvents: ErrorEvent[] = [];
  private userInteractions: UserInteraction[] = [];
  private metricsTimer: NodeJS.Timeout | null = null;
  private lastQualityCheck: number = 0;
  private sessionStartTime: number;
  private lastBufferLevel: number = 0;
  private currentBufferingStart: number | null = null;

  constructor(streamId: number, streamName: string, streamType: string) {
    this.sessionStartTime = Date.now();
    this.session = {
      sessionId: this.generateSessionId(),
      streamId,
      streamName,
      streamType,
      startTime: this.sessionStartTime,
      duration: 0,
      totalWatchTime: 0,
      bufferingTime: 0,
      stallingEvents: 0,
      qualityChanges: 0,
      averageBitrate: 0,
      peakBitrate: 0,
      droppedFrames: 0,
      errorCount: 0,
      userAgent: navigator.userAgent,
      resolution: this.getScreenResolution(),
      networkType: this.getNetworkType(),
    };

    this.startMetricsCollection();
  }

  /**
   * Record a quality metric sample
   */
  recordQualityMetric(
    bitrate: number,
    resolution: string,
    bufferLevel: number,
    droppedFrames: number,
    bandwidth: number
  ): void {
    const now = Date.now();
    
    const metric: QualityMetric = {
      timestamp: now,
      bitrate,
      resolution,
      bufferLevel,
      droppedFrames,
      bandwidth,
    };

    this.qualityMetrics.push(metric);
    
    // Update session metrics
    this.session.averageBitrate = this.calculateAverageBitrate();
    this.session.peakBitrate = Math.max(this.session.peakBitrate, bitrate);
    this.session.droppedFrames = droppedFrames;

    // Detect quality changes
    if (this.qualityMetrics.length > 1) {
      const previousMetric = this.qualityMetrics[this.qualityMetrics.length - 2];
      if (previousMetric.bitrate !== bitrate || previousMetric.resolution !== resolution) {
        this.session.qualityChanges++;
      }
    }

    // Keep only recent metrics (last 10 minutes)
    const tenMinutesAgo = now - 10 * 60 * 1000;
    this.qualityMetrics = this.qualityMetrics.filter(m => m.timestamp > tenMinutesAgo);

    this.lastBufferLevel = bufferLevel;
    this.lastQualityCheck = now;
  }

  /**
   * Record a buffering event
   */
  recordBufferingStart(reason: BufferingEvent['reason'] = 'rebuffering'): void {
    if (this.currentBufferingStart) return; // Already buffering

    this.currentBufferingStart = Date.now();
    this.session.stallingEvents++;
  }

  /**
   * Record end of buffering event
   */
  recordBufferingEnd(): void {
    if (!this.currentBufferingStart) return;

    const now = Date.now();
    const duration = now - this.currentBufferingStart;
    
    const bufferingEvent: BufferingEvent = {
      timestamp: this.currentBufferingStart,
      duration,
      bufferLevel: this.lastBufferLevel,
      reason: 'rebuffering',
    };

    this.bufferingEvents.push(bufferingEvent);
    this.session.bufferingTime += duration;
    this.currentBufferingStart = null;

    // Keep only recent events
    const oneHourAgo = now - 60 * 60 * 1000;
    this.bufferingEvents = this.bufferingEvents.filter(e => e.timestamp > oneHourAgo);
  }

  /**
   * Record an error event
   */
  recordError(
    type: ErrorEvent['type'],
    code: string,
    message: string,
    fatal: boolean = false,
    recovered: boolean = false
  ): void {
    const errorEvent: ErrorEvent = {
      timestamp: Date.now(),
      type,
      code,
      message,
      fatal,
      recovered,
    };

    this.errorEvents.push(errorEvent);
    this.session.errorCount++;

    // Keep only recent errors
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.errorEvents = this.errorEvents.filter(e => e.timestamp > oneHourAgo);
  }

  /**
   * Record user interaction
   */
  recordUserInteraction(action: UserInteraction['action'], value?: number | string): void {
    const interaction: UserInteraction = {
      timestamp: Date.now(),
      action,
      value,
    };

    this.userInteractions.push(interaction);

    // Keep only recent interactions
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.userInteractions = this.userInteractions.filter(i => i.timestamp > oneHourAgo);
  }

  /**
   * Update watch time
   */
  updateWatchTime(currentTime: number, duration: number): void {
    this.session.duration = duration;
    this.session.totalWatchTime = currentTime;
  }

  /**
   * Get current session data
   */
  getSession(): PlaybackSession {
    return { ...this.session };
  }

  /**
   * Get quality metrics summary
   */
  getQualityMetrics(): {
    current: QualityMetric | null;
    average: Partial<QualityMetric>;
    peak: Partial<QualityMetric>;
    stability: number;
  } {
    if (this.qualityMetrics.length === 0) {
      return {
        current: null,
        average: {},
        peak: {},
        stability: 0,
      };
    }

    const current = this.qualityMetrics[this.qualityMetrics.length - 1];
    const averageBitrate = this.calculateAverageBitrate();
    const averageBufferLevel = this.qualityMetrics.reduce((sum, m) => sum + m.bufferLevel, 0) / this.qualityMetrics.length;
    const peakBitrate = Math.max(...this.qualityMetrics.map(m => m.bitrate));
    const peakBufferLevel = Math.max(...this.qualityMetrics.map(m => m.bufferLevel));

    // Calculate stability (lower is more stable)
    const bitrateVariance = this.calculateBitrateVariance();
    const stability = Math.max(0, Math.min(100, 100 - (bitrateVariance * 10)));

    return {
      current,
      average: {
        bitrate: averageBitrate,
        bufferLevel: averageBufferLevel,
      },
      peak: {
        bitrate: peakBitrate,
        bufferLevel: peakBufferLevel,
      },
      stability,
    };
  }

  /**
   * Get buffering statistics
   */
  getBufferingStats(): {
    totalEvents: number;
    totalTime: number;
    averageDuration: number;
    bufferingRatio: number;
  } {
    const totalEvents = this.bufferingEvents.length;
    const totalTime = this.bufferingEvents.reduce((sum, e) => sum + e.duration, 0);
    const averageDuration = totalEvents > 0 ? totalTime / totalEvents : 0;
    const bufferingRatio = this.session.totalWatchTime > 0 ? totalTime / this.session.totalWatchTime : 0;

    return {
      totalEvents,
      totalTime,
      averageDuration,
      bufferingRatio,
    };
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    fatalErrors: number;
    recoveredErrors: number;
    errorRate: number;
    errorsByType: Record<string, number>;
  } {
    const totalErrors = this.errorEvents.length;
    const fatalErrors = this.errorEvents.filter(e => e.fatal).length;
    const recoveredErrors = this.errorEvents.filter(e => e.recovered).length;
    const sessionDuration = (Date.now() - this.sessionStartTime) / 1000 / 60; // minutes
    const errorRate = sessionDuration > 0 ? totalErrors / sessionDuration : 0;

    const errorsByType = this.errorEvents.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalErrors,
      fatalErrors,
      recoveredErrors,
      errorRate,
      errorsByType,
    };
  }

  /**
   * Get user interaction summary
   */
  getUserInteractionStats(): Record<string, number> {
    return this.userInteractions.reduce((acc, interaction) => {
      acc[interaction.action] = (acc[interaction.action] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * End the session and get final report
   */
  endSession(): PlaybackSession {
    this.session.endTime = Date.now();
    
    if (this.currentBufferingStart) {
      this.recordBufferingEnd();
    }

    this.stopMetricsCollection();
    
    return this.getSession();
  }

  /**
   * Export analytics data for reporting
   */
  exportData(): {
    session: PlaybackSession;
    qualityMetrics: QualityMetric[];
    bufferingEvents: BufferingEvent[];
    errorEvents: ErrorEvent[];
    userInteractions: UserInteraction[];
  } {
    return {
      session: this.getSession(),
      qualityMetrics: [...this.qualityMetrics],
      bufferingEvents: [...this.bufferingEvents],
      errorEvents: [...this.errorEvents],
      userInteractions: [...this.userInteractions],
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopMetricsCollection();
  }

  // Private methods

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getScreenResolution(): string {
    return `${screen.width}x${screen.height}`;
  }

  private getNetworkType(): string | undefined {
    // @ts-ignore - navigator.connection is experimental
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    return connection?.effectiveType || connection?.type;
  }

  private calculateAverageBitrate(): number {
    if (this.qualityMetrics.length === 0) return 0;
    return this.qualityMetrics.reduce((sum, m) => sum + m.bitrate, 0) / this.qualityMetrics.length;
  }

  private calculateBitrateVariance(): number {
    if (this.qualityMetrics.length < 2) return 0;
    
    const bitrates = this.qualityMetrics.map(m => m.bitrate);
    const mean = bitrates.reduce((a, b) => a + b, 0) / bitrates.length;
    const variance = bitrates.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / bitrates.length;
    
    return mean > 0 ? Math.sqrt(variance) / mean : 0; // Coefficient of variation
  }

  private startMetricsCollection(): void {
    // Collect metrics every 5 seconds
    this.metricsTimer = setInterval(() => {
      // This will be called by the video player to update metrics
    }, 5000);
  }

  private stopMetricsCollection(): void {
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = null;
    }
  }
}

export default PlaybackAnalytics;
