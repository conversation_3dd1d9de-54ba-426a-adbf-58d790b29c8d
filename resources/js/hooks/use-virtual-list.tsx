import { useState, useEffect, useCallback, useMemo } from 'react';

interface VirtualListOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number; // Number of items to render outside visible area
  scrollElement?: HTMLElement | null;
}

interface VirtualListReturn<T> {
  virtualItems: Array<{
    index: number;
    start: number;
    end: number;
    item: T;
  }>;
  totalHeight: number;
  scrollToIndex: (index: number) => void;
  containerProps: {
    style: React.CSSProperties;
  };
  innerProps: {
    style: React.CSSProperties;
  };
}

/**
 * Virtual List Hook
 * 
 * Provides efficient rendering for large lists by only rendering
 * visible items plus a small overscan buffer.
 */
export function useVirtualList<T>(
  items: T[],
  options: VirtualListOptions
): VirtualListReturn<T> {
  const {
    itemHeight,
    containerHeight,
    overscan = 5,
    scrollElement
  } = options;

  const [scrollTop, setScrollTop] = useState(0);

  // Calculate total height
  const totalHeight = useMemo(() => {
    return items.length * itemHeight;
  }, [items.length, itemHeight]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, start - overscan),
      end: Math.min(items.length - 1, end + overscan)
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length]);

  // Create virtual items
  const virtualItems = useMemo(() => {
    const result = [];
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      if (items[i] !== undefined) {
        result.push({
          index: i,
          start: i * itemHeight,
          end: (i + 1) * itemHeight,
          item: items[i]
        });
      }
    }
    
    return result;
  }, [visibleRange, items, itemHeight]);

  // Handle scroll events
  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLElement;
    setScrollTop(target.scrollTop);
  }, []);

  // Set up scroll listener
  useEffect(() => {
    const element = scrollElement || window;
    
    element.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      element.removeEventListener('scroll', handleScroll);
    };
  }, [scrollElement, handleScroll]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const targetScrollTop = index * itemHeight;
    const element = scrollElement || window;
    
    if (element === window) {
      window.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    } else {
      (element as HTMLElement).scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }
  }, [itemHeight, scrollElement]);

  // Container props
  const containerProps = useMemo(() => ({
    style: {
      height: containerHeight,
      overflow: 'auto',
      position: 'relative' as const,
    }
  }), [containerHeight]);

  // Inner container props
  const innerProps = useMemo(() => ({
    style: {
      height: totalHeight,
      position: 'relative' as const,
    }
  }), [totalHeight]);

  return {
    virtualItems,
    totalHeight,
    scrollToIndex,
    containerProps,
    innerProps,
  };
}

/**
 * Virtual Grid Hook
 * 
 * Provides efficient rendering for large grids by only rendering
 * visible items in a 2D grid layout.
 */
interface VirtualGridOptions {
  itemWidth: number;
  itemHeight: number;
  containerWidth: number;
  containerHeight: number;
  gap?: number;
  overscan?: number;
}

interface VirtualGridReturn<T> {
  virtualItems: Array<{
    index: number;
    row: number;
    col: number;
    x: number;
    y: number;
    item: T;
  }>;
  totalHeight: number;
  columnsPerRow: number;
  scrollToIndex: (index: number) => void;
  containerProps: {
    style: React.CSSProperties;
  };
  innerProps: {
    style: React.CSSProperties;
  };
}

export function useVirtualGrid<T>(
  items: T[],
  options: VirtualGridOptions
): VirtualGridReturn<T> {
  const {
    itemWidth,
    itemHeight,
    containerWidth,
    containerHeight,
    gap = 0,
    overscan = 2
  } = options;

  const [scrollTop, setScrollTop] = useState(0);

  // Calculate columns per row
  const columnsPerRow = useMemo(() => {
    return Math.floor((containerWidth + gap) / (itemWidth + gap));
  }, [containerWidth, itemWidth, gap]);

  // Calculate total rows and height
  const totalRows = useMemo(() => {
    return Math.ceil(items.length / columnsPerRow);
  }, [items.length, columnsPerRow]);

  const totalHeight = useMemo(() => {
    return totalRows * (itemHeight + gap) - gap;
  }, [totalRows, itemHeight, gap]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const rowHeight = itemHeight + gap;
    const startRow = Math.floor(scrollTop / rowHeight);
    const endRow = Math.min(
      startRow + Math.ceil(containerHeight / rowHeight),
      totalRows - 1
    );

    return {
      startRow: Math.max(0, startRow - overscan),
      endRow: Math.min(totalRows - 1, endRow + overscan)
    };
  }, [scrollTop, itemHeight, gap, containerHeight, overscan, totalRows]);

  // Create virtual items
  const virtualItems = useMemo(() => {
    const result = [];
    
    for (let row = visibleRange.startRow; row <= visibleRange.endRow; row++) {
      for (let col = 0; col < columnsPerRow; col++) {
        const index = row * columnsPerRow + col;
        
        if (index < items.length && items[index] !== undefined) {
          result.push({
            index,
            row,
            col,
            x: col * (itemWidth + gap),
            y: row * (itemHeight + gap),
            item: items[index]
          });
        }
      }
    }
    
    return result;
  }, [visibleRange, columnsPerRow, items, itemWidth, itemHeight, gap]);

  // Handle scroll events
  const handleScroll = useCallback((event: Event) => {
    const target = event.target as HTMLElement;
    setScrollTop(target.scrollTop);
  }, []);

  // Set up scroll listener
  useEffect(() => {
    const element = window; // For grid, typically use window scroll
    
    element.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      element.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number) => {
    const row = Math.floor(index / columnsPerRow);
    const targetScrollTop = row * (itemHeight + gap);
    
    window.scrollTo({
      top: targetScrollTop,
      behavior: 'smooth'
    });
  }, [columnsPerRow, itemHeight, gap]);

  // Container props
  const containerProps = useMemo(() => ({
    style: {
      height: containerHeight,
      overflow: 'auto',
      position: 'relative' as const,
    }
  }), [containerHeight]);

  // Inner container props
  const innerProps = useMemo(() => ({
    style: {
      height: totalHeight,
      position: 'relative' as const,
    }
  }), [totalHeight]);

  return {
    virtualItems,
    totalHeight,
    columnsPerRow,
    scrollToIndex,
    containerProps,
    innerProps,
  };
}
