import { type ReactNode } from 'react';

// Components
import AuthLayoutTemplate from '@/layouts/auth/auth-simple-layout';

interface AuthLayoutProps {
    children: ReactNode;
    title: string;
    description: string;
}

/**
 * Authentication Layout
 *
 * A wrapper around the AuthSimpleLayout that provides a consistent layout
 * for all authentication pages (login, register, etc.).
 */
export default function AuthLayout({ children, title, description, ...props }: AuthLayoutProps) {
    return (
        <AuthLayoutTemplate title={title} description={description} {...props}>
            {children}
        </AuthLayoutTemplate>
    );
}
