import { ReactNode } from 'react';
import UnifiedLayout from '@/layouts/unified-layout';

/**
 * Main Layout Props
 */
interface MainLayoutProps {
  children: ReactNode;
}

/**
 * Main Layout Component
 *
 * This is now a wrapper around the UnifiedLayout component.
 * Kept for backward compatibility with existing code.
 */
export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <UnifiedLayout>
      {children}
    </UnifiedLayout>
  );
}
