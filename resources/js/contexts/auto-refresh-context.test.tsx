import { render, screen, fireEvent, act } from '@testing-library/react';
import { AutoRefreshProvider, useAutoRefresh } from './auto-refresh-context';
import { vi, beforeEach, afterEach, describe, expect, it } from 'vitest';
import React from 'react';
import { router } from '@inertiajs/react';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
  router: {
    reload: vi.fn(),
  },
}));

// Create a mock implementation of localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    clear: vi.fn(() => {
      store = {};
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
  };
})();

// Replace the global localStorage with our mock
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

// Mock localStorage hook
vi.mock('@/hooks/use-local-storage', () => ({
  useLocalStorage: (key: string, initialValue: any) => {
    const [value, setValue] = React.useState(initialValue);
    return [value, setValue];
  },
}));

// Mock useInterval
vi.mock('@/hooks/useInterval', () => ({
  useInterval: (callback: () => void, delay: number | null) => {
    React.useEffect(() => {
      if (delay !== null) {
        const id = setInterval(callback, delay);
        return () => clearInterval(id);
      }
    }, [callback, delay]);
  },
}));

// Test component that uses the context
const TestComponent = () => {
  const {
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval,
    isRefreshing,
    refreshData,
    temporarilyDisableAutoRefresh,
    restorePreviousAutoRefreshState
  } = useAutoRefresh();

  return (
    <div>
      <div data-testid="auto-refresh-status">{autoRefresh ? 'enabled' : 'disabled'}</div>
      <div data-testid="refresh-interval">{refreshInterval}</div>
      <div data-testid="is-refreshing">{isRefreshing ? 'refreshing' : 'idle'}</div>
      <button onClick={() => setAutoRefresh(!autoRefresh)}>Toggle Auto Refresh</button>
      <button onClick={() => setRefreshInterval(60000)}>Set Interval to 60s</button>
      <button onClick={refreshData}>Refresh Now</button>
      <button onClick={temporarilyDisableAutoRefresh}>Temporarily Disable</button>
      <button onClick={restorePreviousAutoRefreshState}>Restore Auto Refresh</button>
    </div>
  );
};

describe('AutoRefreshContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should provide default values', () => {
    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('enabled');
    expect(screen.getByTestId('refresh-interval').textContent).toBe('30000');
    expect(screen.getByTestId('is-refreshing').textContent).toBe('idle');
  });

  it('should toggle auto refresh when setAutoRefresh is called', () => {
    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    fireEvent.click(screen.getByText('Toggle Auto Refresh'));

    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('disabled');
  });

  it('should update refresh interval when setRefreshInterval is called', () => {
    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    fireEvent.click(screen.getByText('Set Interval to 60s'));

    expect(screen.getByTestId('refresh-interval').textContent).toBe('60000');
  });

  it('should call router.reload when refreshData is called', async () => {
    // Mock the router.reload function
    const mockReload = vi.fn();
    vi.mocked(router.reload).mockImplementation(mockReload);

    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    // Click the refresh button
    act(() => {
      fireEvent.click(screen.getByText('Refresh Now'));
    });

    // Verify router.reload was called
    expect(mockReload).toHaveBeenCalledWith({
      preserveScroll: true,
      preserveState: true,
    });
  });

  it('should temporarily disable auto refresh', () => {
    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    // Initially enabled
    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('enabled');

    // Temporarily disable
    fireEvent.click(screen.getByText('Temporarily Disable'));

    // Should be disabled now
    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('disabled');
  });

  it('should restore previous auto refresh state', () => {
    render(
      <AutoRefreshProvider>
        <TestComponent />
      </AutoRefreshProvider>
    );

    // Initially enabled
    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('enabled');

    // Temporarily disable
    fireEvent.click(screen.getByText('Temporarily Disable'));

    // Should be disabled now
    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('disabled');

    // Restore previous state
    fireEvent.click(screen.getByText('Restore Auto Refresh'));

    // Should be enabled again
    expect(screen.getByTestId('auto-refresh-status').textContent).toBe('enabled');
  });
});
