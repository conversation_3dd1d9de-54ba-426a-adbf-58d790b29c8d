import { type BreadcrumbItem } from '@/types';
import { Transition } from '@headlessui/react';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';

import HeadingSmall from '@/components/heading-small';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import SettingsLayout from '@/layouts/settings/settings-layout';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Preferences',
        href: '/settings/preferences',
    },
];

type PreferencesForm = {
    playback: {
        default_volume: number;
        autoplay: boolean;
        default_speed: number;
        mute_by_default: boolean;
        remember_position: boolean;
    };
    ui: {
        default_view_mode: string;
        items_per_page: number;
        default_sorting: string;
        default_sorting_direction: string;
        show_stream_details_on_hover: boolean;
    };
    notifications: {
        email_notifications: boolean;
        browser_notifications: boolean;
        notification_frequency: string;
    };
}

export default function Preferences({ preferences }: { preferences: PreferencesForm }) {
    const { data, setData, post, processing, errors, recentlySuccessful } = useForm<PreferencesForm>(preferences);
    const [volume, setVolume] = useState(data.playback.default_volume);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('preferences.update'), {
            preserveScroll: true,
        });
    };

    const handleVolumeChange = (value: number[]) => {
        setVolume(value[0]);
        setData('playback.default_volume', value[0]);
    };

    return (
        <SettingsLayout breadcrumbs={breadcrumbs}>
            <Head title="Preferences" />

            <div className="py-6">
                <HeadingSmall title="User Preferences" description="Customize your experience with IPTV Manager" />

                <div className="mt-8 max-w-2xl">
                    <form onSubmit={submit} className="space-y-6">
                        {/* Playback Preferences */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Playback Preferences</CardTitle>
                                <CardDescription>
                                    Configure how streams are played in the application
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="default_volume">Default Volume ({volume}%)</Label>
                                    <Slider
                                        id="default_volume"
                                        value={[data.playback.default_volume]}
                                        min={0}
                                        max={100}
                                        step={1}
                                        onValueChange={handleVolumeChange}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="autoplay">Autoplay Streams</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Automatically play streams when opened
                                        </p>
                                    </div>
                                    <Switch
                                        id="autoplay"
                                        checked={data.playback.autoplay}
                                        onCheckedChange={(checked) => setData('playback.autoplay', checked)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="default_speed">Default Playback Speed</Label>
                                    <Select
                                        value={data.playback.default_speed.toString()}
                                        onValueChange={(value) => setData('playback.default_speed', parseFloat(value))}
                                    >
                                        <SelectTrigger id="default_speed">
                                            <SelectValue placeholder="Select playback speed" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0.5">0.5x</SelectItem>
                                            <SelectItem value="0.75">0.75x</SelectItem>
                                            <SelectItem value="1">1x (Normal)</SelectItem>
                                            <SelectItem value="1.25">1.25x</SelectItem>
                                            <SelectItem value="1.5">1.5x</SelectItem>
                                            <SelectItem value="2">2x</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="mute_by_default">Mute by Default</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Start streams with audio muted
                                        </p>
                                    </div>
                                    <Switch
                                        id="mute_by_default"
                                        checked={data.playback.mute_by_default}
                                        onCheckedChange={(checked) => setData('playback.mute_by_default', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="remember_position">Remember Playback Position</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Resume streams from where you left off
                                        </p>
                                    </div>
                                    <Switch
                                        id="remember_position"
                                        checked={data.playback.remember_position}
                                        onCheckedChange={(checked) => setData('playback.remember_position', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* UI Preferences */}
                        <Card>
                            <CardHeader>
                                <CardTitle>UI Preferences</CardTitle>
                                <CardDescription>
                                    Configure how the application interface behaves
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="default_view_mode">Default View Mode</Label>
                                    <RadioGroup
                                        id="default_view_mode"
                                        value={data.ui.default_view_mode}
                                        onValueChange={(value) => setData('ui.default_view_mode', value)}
                                        className="flex space-x-4"
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="grid" id="grid" />
                                            <Label htmlFor="grid">Grid</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="list" id="list" />
                                            <Label htmlFor="list">List</Label>
                                        </div>
                                    </RadioGroup>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="items_per_page">Items Per Page</Label>
                                    <Select
                                        value={data.ui.items_per_page.toString()}
                                        onValueChange={(value) => setData('ui.items_per_page', parseInt(value))}
                                    >
                                        <SelectTrigger id="items_per_page">
                                            <SelectValue placeholder="Select items per page" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="10">10</SelectItem>
                                            <SelectItem value="25">25</SelectItem>
                                            <SelectItem value="50">50</SelectItem>
                                            <SelectItem value="100">100</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="default_sorting">Default Sorting</Label>
                                    <Select
                                        value={data.ui.default_sorting}
                                        onValueChange={(value) => setData('ui.default_sorting', value)}
                                    >
                                        <SelectTrigger id="default_sorting">
                                            <SelectValue placeholder="Select default sorting" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="name">Name</SelectItem>
                                            <SelectItem value="date_added">Date Added</SelectItem>
                                            <SelectItem value="last_checked">Last Checked</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="default_sorting_direction">Default Sorting Direction</Label>
                                    <RadioGroup
                                        id="default_sorting_direction"
                                        value={data.ui.default_sorting_direction}
                                        onValueChange={(value) => setData('ui.default_sorting_direction', value)}
                                        className="flex space-x-4"
                                    >
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="asc" id="asc" />
                                            <Label htmlFor="asc">Ascending</Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="desc" id="desc" />
                                            <Label htmlFor="desc">Descending</Label>
                                        </div>
                                    </RadioGroup>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="show_stream_details_on_hover">Show Stream Details on Hover</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Display stream information when hovering over a stream
                                        </p>
                                    </div>
                                    <Switch
                                        id="show_stream_details_on_hover"
                                        checked={data.ui.show_stream_details_on_hover}
                                        onCheckedChange={(checked) => setData('ui.show_stream_details_on_hover', checked)}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        {/* Notification Preferences */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Notification Preferences</CardTitle>
                                <CardDescription>
                                    Configure how you receive notifications
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="email_notifications">Email Notifications</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Receive notifications via email
                                        </p>
                                    </div>
                                    <Switch
                                        id="email_notifications"
                                        checked={data.notifications.email_notifications}
                                        onCheckedChange={(checked) => setData('notifications.email_notifications', checked)}
                                    />
                                </div>

                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="browser_notifications">Browser Notifications</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Receive notifications in your browser
                                        </p>
                                    </div>
                                    <Switch
                                        id="browser_notifications"
                                        checked={data.notifications.browser_notifications}
                                        onCheckedChange={(checked) => setData('notifications.browser_notifications', checked)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="notification_frequency">Notification Frequency</Label>
                                    <Select
                                        value={data.notifications.notification_frequency}
                                        onValueChange={(value) => setData('notifications.notification_frequency', value)}
                                    >
                                        <SelectTrigger id="notification_frequency">
                                            <SelectValue placeholder="Select notification frequency" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="immediate">Immediate</SelectItem>
                                            <SelectItem value="daily">Daily Digest</SelectItem>
                                            <SelectItem value="weekly">Weekly Digest</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </CardContent>
                        </Card>

                        <div className="flex items-center justify-end gap-4">
                            <Button type="submit" disabled={processing}>
                                Save Preferences
                            </Button>

                            <Transition
                                show={recentlySuccessful}
                                enter="transition ease-in-out"
                                enterFrom="opacity-0"
                                leave="transition ease-in-out"
                                leaveTo="opacity-0"
                            >
                                <p className="text-sm text-green-500">Saved.</p>
                            </Transition>
                        </div>
                    </form>
                </div>
            </div>
        </SettingsLayout>
    );
}
