import { Head, useForm } from '@inertiajs/react';
import { FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import SettingsLayout from '@/layouts/settings/settings-layout';
import { BreadcrumbItem } from '@/types';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface StreamSettingsProps {
  settings: {
    validation_timeout: number;
    validation_max_size: number;
    validation_attempts: number;
  };
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Stream Settings',
    href: '/settings/stream',
  },
];

export default function Stream({ settings }: StreamSettingsProps) {
  const { data, setData, post, processing, errors } = useForm({
    validation_timeout: settings.validation_timeout,
    validation_max_size: settings.validation_max_size,
    validation_attempts: settings.validation_attempts,
  });

  function handleSubmit(e: FormEvent) {
    e.preventDefault();
    post(route('stream.settings.update'));
  }

  // Format bytes to human-readable format
  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  return (
    <SettingsLayout breadcrumbs={breadcrumbs}>
      <Head title="Stream Settings" />

      <div className="py-4">
        <div className="w-full space-y-6">
          <div className="p-4 sm:p-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <section className="max-w-xl">
              <header>
                <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">Stream Validation Settings</h2>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  Configure how streams are validated and checked in your application.
                </p>
              </header>

              <Separator className="my-6" />

              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Timeout Issues</AlertTitle>
                <AlertDescription>
                  If you're experiencing timeout errors when validating streams, try increasing the timeout value and reducing the maximum download size.
                </AlertDescription>
              </Alert>

              <form onSubmit={handleSubmit} className="mt-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Validation Settings</CardTitle>
                    <CardDescription>
                      Configure timeout and download limits for stream validation
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="validation_timeout">Validation Timeout (seconds)</Label>
                        <Input
                          id="validation_timeout"
                          type="number"
                          value={data.validation_timeout}
                          onChange={(e) => setData('validation_timeout', parseInt(e.target.value))}
                          min={5}
                          max={120}
                        />
                        {errors.validation_timeout && (
                          <p className="text-sm text-red-500">{errors.validation_timeout}</p>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Maximum time in seconds to wait for a stream to respond. Increase this value for slow streams.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="validation_max_size">Maximum Download Size</Label>
                        <Input
                          id="validation_max_size"
                          type="number"
                          value={data.validation_max_size}
                          onChange={(e) => setData('validation_max_size', parseInt(e.target.value))}
                          min={524288}
                          max={10485760}
                        />
                        {errors.validation_max_size && (
                          <p className="text-sm text-red-500">{errors.validation_max_size}</p>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Maximum bytes to download when validating a stream ({formatBytes(data.validation_max_size)}). Reduce this for faster validation.
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="validation_attempts">Validation Attempts</Label>
                      <Input
                        id="validation_attempts"
                        type="number"
                        value={data.validation_attempts}
                        onChange={(e) => setData('validation_attempts', parseInt(e.target.value))}
                        min={1}
                        max={5}
                      />
                      {errors.validation_attempts && (
                        <p className="text-sm text-red-500">{errors.validation_attempts}</p>
                      )}
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Number of times to attempt validating a stream before marking it as error.
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button type="submit" disabled={processing}>
                      Save Settings
                    </Button>
                  </CardFooter>
                </Card>
              </form>
            </section>
          </div>
        </div>
      </div>
    </SettingsLayout>
  );
}
