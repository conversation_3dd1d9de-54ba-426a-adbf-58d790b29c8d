import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { PageProps } from '@/types';
import { GitHubCredential } from '@/types/github';
import { PreferredChannel } from '@/types/preferred-channels';
import AppLayout from '@/layouts/main-layout';
import ExportOptionsForm from '@/components/export/export-options-form';
import ExportJobsList from '@/components/export/export-jobs-list';

interface Stream {
  id: number;
  name: string;
  status: string;
  url: string;
  tvg_id: string | null;
  tvg_name: string | null;
  tvg_logo: string | null;
  group_title: string | null;
  type: string;
  exclude_from_export: boolean;
}

interface ExportJob {
  id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path: string | null;
  created_at: string;
  expires_at: string;
  metadata?: {
    github_export?: boolean;
    github_repo?: string;
    github_path?: string;
    github_note?: string;
    github_result?: {
      success: boolean;
      error?: string;
      no_changes?: boolean;
    };
  };
}

interface Playlist {
  id: number;
  name: string;
  description: string | null;
  is_public: boolean;
  streams_count: number;
}

interface ExportIndexProps extends PageProps {
  streamCounts: {
    online: number;
    offline: number;
    pending: number;
    error: number;
  };
  streams: Stream[];
  exports: ExportJob[];
  githubCredentials: GitHubCredential[];
  preferredChannels: PreferredChannel[];
  playlists: Playlist[];
}

interface ExportIndexProps extends PageProps {
  streamCounts: {
    online: number;
    offline: number;
    pending: number;
    error: number;
  };
  exports: ExportJob[];
  githubCredentials: GitHubCredential[];
  preferredChannels: PreferredChannel[];
  playlists: Playlist[];
}

export default function ExportIndex({
  streamCounts,
  exports,
  githubCredentials,
  preferredChannels,
  playlists
}: ExportIndexProps) {
  // State for refreshing exports list
  const [refreshExports, setRefreshExports] = useState(0);

  // Handle export form submission
  const handleExportSubmitted = () => {
    // Refresh exports list
    setRefreshExports(prev => prev + 1);
  };

  // Handle export deletion
  const handleExportDeleted = () => {
    // Refresh exports list
    setRefreshExports(prev => prev + 1);
  };

  return (
    <AppLayout>
      <Head title="Export Streams" />

      <div className="py-4 px-2 w-full max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Export Streams</h1>
          <p className="text-muted-foreground">
            Export your streams as an M3U playlist file.
          </p>
        </div>

        {/* Export Options Form */}
        <ExportOptionsForm
          streamCounts={streamCounts}
          githubCredentials={githubCredentials}
          preferredChannels={preferredChannels}
          playlists={playlists}
          onSubmit={handleExportSubmitted}
        />

        {/* Export Jobs List */}
        <div className="mb-6">
          <ExportJobsList
            exports={exports}
            onExportDeleted={handleExportDeleted}
          />
        </div>
      </div>
    </AppLayout>
  );
}
