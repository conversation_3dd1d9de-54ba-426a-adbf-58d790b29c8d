import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ParentCategory {
  id: number;
  name: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  parent_id: number | null;
}

interface CategoryEditProps extends PageProps {
  category: Category;
  parentCategories: ParentCategory[];
}

export default function CategoryEdit({ auth, category, parentCategories, errors }: CategoryEditProps) {
  const form = useForm({
    name: category.name,
    slug: category.slug,
    description: category.description || '',
    parent_id: category.parent_id ? category.parent_id.toString() : 'none',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    form.put(route('categories.update', category.slug));
  };

  return (
    <AppLayout>
      <Head title={`Edit ${category.name}`} />

      <div className="py-12">
        <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold mb-6">Edit Category</h1>

          <Card>
            <CardHeader>
              <CardTitle>Category Details</CardTitle>
              <CardDescription>
                Update the category information.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={form.data.name}
                      onChange={(e) => form.setData('name', e.target.value)}
                      required
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">Slug</Label>
                    <Input
                      id="slug"
                      value={form.data.slug}
                      onChange={(e) => form.setData('slug', e.target.value)}
                      placeholder="auto-generated-if-empty"
                    />
                    <p className="text-xs text-muted-foreground">
                      Leave empty to auto-generate from name. Used in URLs.
                    </p>
                    {errors.slug && (
                      <p className="text-sm text-red-500">{errors.slug}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description (optional)</Label>
                    <Textarea
                      id="description"
                      value={form.data.description}
                      onChange={(e) => form.setData('description', e.target.value)}
                      rows={4}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="parent_id">Parent Category (optional)</Label>
                    <Select
                      value={form.data.parent_id}
                      onValueChange={(value) => form.setData('parent_id', value)}
                    >
                      <SelectTrigger id="parent_id">
                        <SelectValue placeholder="No parent (root category)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No parent (root category)</SelectItem>
                        {parentCategories.map((parentCategory) => (
                          <SelectItem key={parentCategory.id} value={parentCategory.id.toString()}>
                            {parentCategory.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Select a parent category to make this a subcategory.
                    </p>
                    {errors.parent_id && (
                      <p className="text-sm text-red-500">{errors.parent_id}</p>
                    )}
                  </div>

                  {Object.keys(errors).length > 0 && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Please fix the errors above before submitting the form.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => window.history.back()}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={form.processing}>
                    {form.processing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
