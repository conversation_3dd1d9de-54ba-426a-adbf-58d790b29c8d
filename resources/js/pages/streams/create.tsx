import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

export default function StreamCreate({ auth, errors }: PageProps) {
  const form = useForm({
    name: '',
    url: '',
    type: 'hls',
    tvg_id: '',
    tvg_name: '',
    tvg_logo: '',
    group_title: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    form.post(route('streams.store'));
  };

  return (
    <AppLayout>
      <Head title="Add Stream" />

      <div className="py-4">
        <div className="w-full">
          <h1 className="text-2xl font-bold mb-6">Add Stream</h1>

          <Card>
            <CardHeader>
              <CardTitle>Stream Details</CardTitle>
              <CardDescription>
                Enter the details of the stream you want to add.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        value={form.data.name}
                        onChange={(e) => form.setData('name', e.target.value)}
                        required
                      />
                      {errors.name && (
                        <p className="text-sm text-red-500">{errors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="type">Stream Type</Label>
                      <Select
                        value={form.data.type}
                        onValueChange={(value) => form.setData('type', value)}
                      >
                        <SelectTrigger id="type">
                          <SelectValue placeholder="Select stream type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hls">HLS (m3u8)</SelectItem>
                          <SelectItem value="mp4">MP4</SelectItem>
                          <SelectItem value="ts">TS</SelectItem>
                          <SelectItem value="rtmp">RTMP</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.type && (
                        <p className="text-sm text-red-500">{errors.type}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="url">Stream URL</Label>
                    <Input
                      id="url"
                      type="url"
                      value={form.data.url}
                      onChange={(e) => form.setData('url', e.target.value)}
                      required
                    />
                    {errors.url && (
                      <p className="text-sm text-red-500">{errors.url}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="tvg_id">TVG ID</Label>
                      <Input
                        id="tvg_id"
                        value={form.data.tvg_id}
                        onChange={(e) => form.setData('tvg_id', e.target.value)}
                      />
                      {errors.tvg_id && (
                        <p className="text-sm text-red-500">{errors.tvg_id}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tvg_name">TVG Name</Label>
                      <Input
                        id="tvg_name"
                        value={form.data.tvg_name}
                        onChange={(e) => form.setData('tvg_name', e.target.value)}
                      />
                      {errors.tvg_name && (
                        <p className="text-sm text-red-500">{errors.tvg_name}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="tvg_logo">Logo URL</Label>
                      <Input
                        id="tvg_logo"
                        type="url"
                        value={form.data.tvg_logo}
                        onChange={(e) => form.setData('tvg_logo', e.target.value)}
                      />
                      {errors.tvg_logo && (
                        <p className="text-sm text-red-500">{errors.tvg_logo}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="group_title">Group Title</Label>
                      <Input
                        id="group_title"
                        value={form.data.group_title}
                        onChange={(e) => form.setData('group_title', e.target.value)}
                      />
                      {errors.group_title && (
                        <p className="text-sm text-red-500">{errors.group_title}</p>
                      )}
                    </div>
                  </div>

                  {Object.keys(errors).length > 0 && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Please fix the errors above before submitting the form.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => window.history.back()}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={form.processing}>
                    {form.processing ? 'Adding...' : 'Add Stream'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
