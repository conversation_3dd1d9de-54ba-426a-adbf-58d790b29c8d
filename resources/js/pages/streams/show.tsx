import React, { useCallback } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { usePageRefresh } from '@/contexts/auto-refresh-context';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Play,
  Pencil,
  RefreshCw,
  ExternalLink,
  Tag as TagIcon,
  FolderTree
} from 'lucide-react';

interface Stream {
  id: number;
  name: string;
  url: string;
  type: string;
  status: string;
  metadata: any;
  last_checked_at: string | null;
  tvg_id: string | null;
  tvg_name: string | null;
  tvg_logo: string | null;
  group_title: string | null;
  categories: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  tags: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  playlists: Array<{
    id: number;
    name: string;
    is_public: boolean;
  }>;
}

interface StreamShowProps extends PageProps {
  stream: Stream;
}

export default function StreamShow({ auth, stream }: StreamShowProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Online</Badge>;
      case 'offline':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" /> Offline</Badge>;
      case 'error':
        return <Badge variant="warning" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Error</Badge>;
      case 'pending':
        return <Badge variant="info" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Create a refresh handler for the stream detail page
  const refreshStream = useCallback(async () => {
    try {
      // Use Inertia.js to reload the stream data
      router.reload({
        only: ['stream'],
      });
    } catch (error) {
      console.error('Failed to refresh stream', error);
      throw error; // Re-throw to let the AutoRefreshContext handle it
    }
  }, []);

  // Register the refresh handler with the AutoRefreshContext
  usePageRefresh(refreshStream);

  return (
    <AppLayout>
      <Head title={stream.name} />

      <div className="py-4">
        <div className="w-full">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">{stream.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(stream.status)}
                <Badge variant="outline">{stream.type.toUpperCase()}</Badge>
                {stream.group_title && (
                  <Badge variant="outline">{stream.group_title}</Badge>
                )}
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href={route('streams.edit', stream.id)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href={route('player.show', stream.id)}>
                  <Play className="mr-2 h-4 w-4" />
                  Play
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href={route('streams.validate', stream.id)} method="post" as="button">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Validate
                </Link>
              </Button>
            </div>
          </div>

          <div className="flex flex-wrap gap-6">
            <div className="flex-1 min-w-[500px] max-w-[800px]">
              <Card>
                <CardHeader>
                  <CardTitle>Stream Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="info">
                    <TabsList className="mb-4">
                      <TabsTrigger value="info">Information</TabsTrigger>
                      <TabsTrigger value="metadata">Metadata</TabsTrigger>
                    </TabsList>

                    <TabsContent value="info" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">URL</h3>
                          <div className="mt-1 flex items-center">
                            <p className="text-sm break-all">{stream.url}</p>
                            <a
                              href={stream.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="ml-2 text-blue-500 hover:text-blue-700"
                            >
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          </div>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                          <p className="mt-1 text-sm">{getStatusBadge(stream.status)}</p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Type</h3>
                          <p className="mt-1 text-sm">{stream.type.toUpperCase()}</p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Last Checked</h3>
                          <p className="mt-1 text-sm">
                            {stream.last_checked_at
                              ? new Date(stream.last_checked_at).toLocaleString()
                              : 'Never'}
                          </p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">TVG ID</h3>
                          <p className="mt-1 text-sm">{stream.tvg_id || 'None'}</p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">TVG Name</h3>
                          <p className="mt-1 text-sm">{stream.tvg_name || 'None'}</p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Group</h3>
                          <p className="mt-1 text-sm">{stream.group_title || 'None'}</p>
                        </div>

                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Logo</h3>
                          {stream.tvg_logo ? (
                            <div className="mt-1 flex items-center">
                              <img
                                src={stream.tvg_logo}
                                alt={stream.name}
                                className="h-8 w-8 object-contain"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                              <a
                                href={stream.tvg_logo}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-2 text-blue-500 hover:text-blue-700 text-sm"
                              >
                                View
                              </a>
                            </div>
                          ) : (
                            <p className="mt-1 text-sm">None</p>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="metadata">
                      {stream.metadata ? (
                        <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                          {JSON.stringify(stream.metadata, null, 2)}
                        </pre>
                      ) : (
                        <p className="text-muted-foreground">No metadata available</p>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </div>

            <div className="flex-1 min-w-[300px] max-w-[400px] space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FolderTree className="mr-2 h-5 w-5" />
                    Categories
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stream.categories.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {stream.categories.map((category) => (
                        <Badge key={category.id} variant="secondary">
                          {category.name}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No categories assigned</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full">
                    Manage Categories
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TagIcon className="mr-2 h-5 w-5" />
                    Tags
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stream.tags.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {stream.tags.map((tag) => (
                        <Badge key={tag.id} variant="secondary">
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No tags assigned</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full">
                    Manage Tags
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Play className="mr-2 h-5 w-5" />
                    Playlists
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stream.playlists.length > 0 ? (
                    <ul className="space-y-2">
                      {stream.playlists.map((playlist) => (
                        <li key={playlist.id} className="flex items-center justify-between">
                          <Link
                            href={route('playlists.show', playlist.id)}
                            className="text-sm hover:underline"
                          >
                            {playlist.name}
                          </Link>
                          {playlist.is_public && (
                            <Badge variant="outline" className="text-xs">Public</Badge>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-muted-foreground">Not in any playlists</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full">
                    Add to Playlist
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
