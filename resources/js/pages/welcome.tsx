import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import {
    PlayCircle,
    ListMusic,
    BarChart3,
    Tv,
    Tag,
    Upload,
    ArrowRight,
    CheckCircle2
} from 'lucide-react';
import AppLogoIcon from '@/components/app-logo-icon';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Welcome to IPTV Manager">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>
            <div className="flex min-h-screen flex-col items-center bg-[#FDFDFC] p-6 text-[#1b1b18] lg:p-8 dark:bg-[#0a0a0a]">
                <header className="mb-6 w-full max-w-[335px] text-sm lg:max-w-4xl">
                    <nav className="flex items-center justify-between gap-4">
                        <div className="flex items-center">
                            <div className="bg-primary text-primary-foreground flex aspect-square h-8 w-8 items-center justify-center rounded-md">
                                <AppLogoIcon className="h-5 w-5 fill-current text-white dark:text-black" />
                            </div>
                            <span className="ml-2 font-semibold">IPTV Manager</span>
                        </div>
                        <div className="flex items-center gap-4">
                            {auth.user ? (
                                <Link
                                    href={route('dashboard')}
                                    className="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                                >
                                    Dashboard
                                </Link>
                            ) : (
                                <>
                                    <Link
                                        href={route('login')}
                                        className="inline-block rounded-sm border border-transparent px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#19140035] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                                    >
                                        Log in
                                    </Link>
                                    <Link
                                        href={route('register')}
                                        className="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                                    >
                                        Register
                                    </Link>
                                </>
                            )}
                        </div>
                    </nav>
                </header>
                <div className="flex w-full items-center justify-center opacity-100 transition-opacity duration-750 lg:grow starting:opacity-0">
                    <main className="flex w-full max-w-[335px] flex-col-reverse lg:max-w-4xl lg:flex-row">
                        <div className="flex-1 rounded-br-lg rounded-bl-lg bg-white p-6 pb-12 text-[14px] leading-[22px] shadow-[inset_0px_0px_0px_1px_rgba(26,26,0,0.16)] lg:rounded-tl-lg lg:rounded-br-none lg:p-16 dark:bg-[#161615] dark:text-[#EDEDEC] dark:shadow-[inset_0px_0px_0px_1px_#fffaed2d]">
                            <h1 className="mb-3 text-2xl font-semibold">Your Complete IPTV Management Solution</h1>
                            <p className="mb-4 text-[#706f6c] dark:text-[#A1A09A]">
                                IPTV Manager is a powerful web application that helps you organize, monitor, and stream your IPTV content with ease.
                            </p>
                            <h2 className="mb-2 mt-6 text-lg font-medium">Key Features</h2>
                            <ul className="mb-6 flex flex-col gap-1">
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <PlayCircle className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>Stream Management</strong> - Add, validate, and organize your IPTV streams
                                    </span>
                                </li>
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <ListMusic className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>Playlist Creation</strong> - Create and export custom M3U playlists
                                    </span>
                                </li>
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <BarChart3 className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>Monitoring</strong> - Track stream health and availability
                                    </span>
                                </li>
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <Tv className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>In-Browser Streaming</strong> - Watch your content directly in the browser
                                    </span>
                                </li>
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <Tag className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>Metadata Management</strong> - Edit and organize stream information
                                    </span>
                                </li>
                                <li className="relative flex items-center gap-4 py-2">
                                    <span className="flex h-8 w-8 items-center justify-center rounded-full bg-[#F8F8F7] dark:bg-[#232322]">
                                        <Upload className="h-5 w-5 text-primary" />
                                    </span>
                                    <span>
                                        <strong>Import Tools</strong> - Import M3U files from URLs or local storage
                                    </span>
                                </li>
                            </ul>
                            <div className="mt-8 flex flex-wrap gap-3 text-sm leading-normal">
                                {auth.user ? (
                                    <Link
                                        href={route('dashboard')}
                                        className="inline-flex items-center gap-2 rounded-sm border border-black bg-[#1b1b18] px-5 py-2 text-sm leading-normal text-white hover:border-black hover:bg-black dark:border-[#eeeeec] dark:bg-[#eeeeec] dark:text-[#1C1C1A] dark:hover:border-white dark:hover:bg-white"
                                    >
                                        Go to Dashboard
                                        <ArrowRight className="h-4 w-4" />
                                    </Link>
                                ) : (
                                    <Link
                                        href={route('register')}
                                        className="inline-flex items-center gap-2 rounded-sm border border-black bg-[#1b1b18] px-5 py-2 text-sm leading-normal text-white hover:border-black hover:bg-black dark:border-[#eeeeec] dark:bg-[#eeeeec] dark:text-[#1C1C1A] dark:hover:border-white dark:hover:bg-white"
                                    >
                                        Get Started
                                        <ArrowRight className="h-4 w-4" />
                                    </Link>
                                )}
                            </div>
                        </div>
                        <div className="relative -mb-px aspect-[335/376] w-full shrink-0 overflow-hidden rounded-t-lg bg-[#f8f8f8] lg:mb-0 lg:-ml-px lg:aspect-auto lg:w-[438px] lg:rounded-t-none lg:rounded-r-lg dark:bg-[#1a1a1a]">
                            <div className="flex h-full w-full flex-col items-center justify-center p-8">
                                <div className="mb-8 flex h-32 w-32 items-center justify-center rounded-full bg-primary/10 p-4">
                                    <AppLogoIcon className="h-20 w-20 text-primary" />
                                </div>

                                <div className="space-y-6 text-center">
                                    <div className="flex flex-col items-center space-y-2">
                                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                                        <p className="text-sm font-medium text-[#1b1b18] dark:text-[#EDEDEC]">Validate and monitor your streams</p>
                                    </div>

                                    <div className="flex flex-col items-center space-y-2">
                                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                                        <p className="text-sm font-medium text-[#1b1b18] dark:text-[#EDEDEC]">Create custom playlists</p>
                                    </div>

                                    <div className="flex flex-col items-center space-y-2">
                                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                                        <p className="text-sm font-medium text-[#1b1b18] dark:text-[#EDEDEC]">Watch directly in your browser</p>
                                    </div>

                                    <div className="flex flex-col items-center space-y-2">
                                        <CheckCircle2 className="h-6 w-6 text-green-500" />
                                        <p className="text-sm font-medium text-[#1b1b18] dark:text-[#EDEDEC]">Import from M3U files or URLs</p>
                                    </div>
                                </div>

                                <div className="mt-8 rounded-lg bg-primary/5 p-4 text-center">
                                    <p className="text-sm font-medium text-[#1b1b18] dark:text-[#EDEDEC]">Manage all your IPTV content in one place</p>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </>
    );
}
