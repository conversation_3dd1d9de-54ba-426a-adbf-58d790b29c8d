import React from 'react';
import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  MoreVertical,
  Plus,
  Search,
  Tag,
  Pencil,
  Trash2
} from 'lucide-react';

interface TagItem {
  id: number;
  name: string;
  slug: string;
  streams_count: number;
}

interface TagsIndexProps extends PageProps {
  tags: {
    data: TagItem[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: {
      url: string | null;
      label: string;
      active: boolean;
    }[];
  };
  popularTags: TagItem[];
  filters: {
    search: string;
    sort: string;
    direction: string;
  };
}

export default function TagsIndex({ auth, tags, popularTags, filters }: TagsIndexProps) {
  const form = useForm({
    search: filters.search || '',
    sort: filters.sort || 'name',
    direction: filters.direction || 'asc',
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    form.get(route('tags.index'), {
      preserveState: true,
    });
  };

  const handleReset = () => {
    form.reset('search');
    form.get(route('tags.index'), {
      preserveState: true,
    });
  };

  return (
    <AppLayout>
      <Head title="Tags" />

      <div className="py-4">
        <div className="w-full">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Tags</h1>
            <Button asChild>
              <Link href={route('tags.create')}>
                <Plus className="mr-2 h-4 w-4" />
                Add Tag
              </Link>
            </Button>
          </div>

          {popularTags.length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Popular Tags</CardTitle>
                <CardDescription>
                  Tags with the most streams
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {popularTags.map((tag) => (
                    <Button
                      key={tag.id}
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link href={route('tags.show', tag.slug)}>
                        <Tag className="mr-2 h-4 w-4" />
                        {tag.name} ({tag.streams_count})
                      </Link>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Filter Tags</CardTitle>
              <CardDescription>
                Use the filters below to find specific tags.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSearch} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Input
                      placeholder="Search by name"
                      value={form.data.search}
                      onChange={(e) => form.setData('search', e.target.value)}
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" type="button" onClick={handleReset} className="flex-1">
                      Reset
                    </Button>
                    <Button type="submit" className="flex-1">
                      <Search className="mr-2 h-4 w-4" />
                      Filter
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Streams</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tags.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8">
                        No tags found. Create a tag to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    tags.data.map((tag) => (
                      <TableRow key={tag.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Tag className="h-4 w-4 text-muted-foreground" />
                            <div className="font-medium">{tag.name}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{tag.streams_count}</Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('tags.show', tag.slug)}>
                                  <Tag className="mr-2 h-4 w-4" />
                                  View Tag
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={route('tags.edit', tag.slug)}>
                                  <Pencil className="mr-2 h-4 w-4" />
                                  Edit Tag
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={route('tags.destroy', tag.slug)}
                                  method="delete"
                                  as="button"
                                  className="w-full text-left text-red-500 hover:text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Tag
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            {tags.last_page > 1 && (
              <CardFooter className="flex items-center justify-between border-t px-6 py-4">
                <div className="text-sm text-muted-foreground">
                  Showing <strong>{tags.data.length}</strong> of{' '}
                  <strong>{tags.total}</strong> tags
                </div>
                <div className="flex space-x-2">
                  {tags.current_page > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link
                        href={route('tags.index', {
                          ...form.data,
                          page: tags.current_page - 1,
                        })}
                        preserveScroll
                      >
                        Previous
                      </Link>
                    </Button>
                  )}
                  {tags.current_page < tags.last_page && (
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link
                        href={route('tags.index', {
                          ...form.data,
                          page: tags.current_page + 1,
                        })}
                        preserveScroll
                      >
                        Next
                      </Link>
                    </Button>
                  )}
                </div>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
