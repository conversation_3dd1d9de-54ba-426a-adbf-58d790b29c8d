import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  MoreVertical,
  Pencil,
  Trash2,
  Tag as TagIcon,
  Plus,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Play
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface Stream {
  id: number;
  name: string;
  url: string;
  type: string;
  status: string;
  tvg_logo: string | null;
}

interface Tag {
  id: number;
  name: string;
  slug: string;
}

interface TagShowProps extends PageProps {
  tag: Tag;
  streams: {
    data: Stream[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: {
      url: string | null;
      label: string;
      active: boolean;
    }[];
  };
  streamCount: number;
  availableStreams: {
    id: number;
    name: string;
    status: string;
  }[];
}

export default function TagShow({ auth, tag, streams, streamCount, availableStreams, errors }: TagShowProps) {
  const [showAddStreamDialog, setShowAddStreamDialog] = useState(false);

  const addStreamForm = useForm({
    stream_id: '',
  });

  const handleAddStream = (e: React.FormEvent) => {
    e.preventDefault();

    addStreamForm.post(route('tags.add-stream', tag.id), {
      onSuccess: () => {
        setShowAddStreamDialog(false);
        addStreamForm.reset();
      },
    });
  };

  const handleRemoveStream = (streamId: number) => {
    if (confirm('Are you sure you want to remove this stream from the tag?')) {
      const url = route('tags.remove-stream', { tag: tag.id, stream: streamId });

      // Use Inertia to make a DELETE request
      window.axios.delete(url).then(() => {
        // The page will refresh with the updated streams
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Online</Badge>;
      case 'offline':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" /> Offline</Badge>;
      case 'error':
        return <Badge variant="warning" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Error</Badge>;
      case 'pending':
        return <Badge variant="info" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <AppLayout>
      <Head title={tag.name} />

      <div className="py-4">
        <div className="w-full">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">{tag.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="flex items-center gap-1">
                  <TagIcon className="h-3 w-3" /> Tag
                </Badge>
                <Badge variant="outline">{streamCount} Streams</Badge>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href={route('tags.edit', tag.slug)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <Button onClick={() => setShowAddStreamDialog(true)} variant="outline" size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Stream
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Streams</CardTitle>
              <CardDescription>
                Streams tagged with {tag.name}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {streams.data.length === 0 ? (
                <div className="p-6 text-center">
                  <p className="text-muted-foreground">No streams with this tag yet.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setShowAddStreamDialog(true)}
                  >
                    Add Stream
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {streams.data.map((stream) => (
                      <TableRow key={stream.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            {stream.tvg_logo && (
                              <img
                                src={stream.tvg_logo}
                                alt={stream.name}
                                className="h-8 w-8 rounded object-contain"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            )}
                            <div>
                              <div className="font-medium">{stream.name}</div>
                              <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                                {stream.url}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(stream.status)}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{stream.type.toUpperCase()}</Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={route('streams.show', stream.id)}>
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={route('player.show', stream.id)}>
                                  <Play className="mr-2 h-4 w-4" />
                                  Play Stream
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleRemoveStream(stream.id)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Remove from Tag
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            {streams.last_page > 1 && (
              <CardFooter className="flex items-center justify-between border-t px-6 py-4">
                <div className="text-sm text-muted-foreground">
                  Showing <strong>{streams.data.length}</strong> of{' '}
                  <strong>{streamCount}</strong> streams
                </div>
                <div className="flex space-x-2">
                  {streams.current_page > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link
                        href={route('tags.show', {
                          tag: tag.slug,
                          page: streams.current_page - 1,
                        })}
                        preserveScroll
                      >
                        Previous
                      </Link>
                    </Button>
                  )}
                  {streams.current_page < streams.last_page && (
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                    >
                      <Link
                        href={route('tags.show', {
                          tag: tag.slug,
                          page: streams.current_page + 1,
                        })}
                        preserveScroll
                      >
                        Next
                      </Link>
                    </Button>
                  )}
                </div>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      {/* Add Stream Dialog */}
      <Dialog open={showAddStreamDialog} onOpenChange={setShowAddStreamDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Stream to Tag</DialogTitle>
            <DialogDescription>
              Select a stream to add to this tag.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddStream}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stream_id">Stream</Label>
                <select
                  id="stream_id"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={addStreamForm.data.stream_id}
                  onChange={(e) => addStreamForm.setData('stream_id', e.target.value)}
                  required
                >
                  <option value="">Select a stream</option>
                  {availableStreams.length === 0 ? (
                    <option value="" disabled>No available streams</option>
                  ) : (
                    availableStreams.map((stream) => (
                      <option key={stream.id} value={stream.id}>
                        {stream.name} ({stream.status})
                      </option>
                    ))
                  )}
                </select>
                {errors.stream_id && (
                  <p className="text-sm text-red-500">{errors.stream_id}</p>
                )}
              </div>
            </div>
            <DialogFooter className="mt-4">
              <Button type="button" variant="outline" onClick={() => setShowAddStreamDialog(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={addStreamForm.processing}>
                Add to Tag
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
