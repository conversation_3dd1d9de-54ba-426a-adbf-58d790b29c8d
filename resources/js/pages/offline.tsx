import React from 'react';
import { Head } from '@inertiajs/react';
import { WifiOff, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

/**
 * Offline Page Component
 * 
 * Displayed when the user is offline and tries to navigate to a page
 * that's not cached by the service worker.
 */
export default function Offline() {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleRetry = () => {
    if (navigator.onLine) {
      window.location.reload();
    } else {
      // Show a message that they're still offline
      alert('You are still offline. Please check your internet connection and try again.');
    }
  };

  return (
    <>
      <Head title="Offline - IPTV Manager" />
      
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center space-y-6">
          {/* Offline Icon */}
          <div className="flex justify-center">
            <div className="rounded-full bg-muted p-6">
              <WifiOff className="h-12 w-12 text-muted-foreground" />
            </div>
          </div>

          {/* Title and Description */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-foreground">
              You're Offline
            </h1>
            <p className="text-muted-foreground">
              It looks like you've lost your internet connection. Some features may not be available until you're back online.
            </p>
          </div>

          {/* Offline Features Available */}
          <div className="bg-muted/50 rounded-lg p-4 text-left">
            <h3 className="font-medium text-foreground mb-2">
              Available Offline:
            </h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• View cached dashboard</li>
              <li>• Browse previously loaded streams</li>
              <li>• Access cached playlists</li>
              <li>• View offline documentation</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              onClick={handleRetry} 
              className="w-full"
              variant="default"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            
            <Button 
              onClick={handleGoHome} 
              variant="outline" 
              className="w-full"
            >
              <Home className="mr-2 h-4 w-4" />
              Go to Dashboard
            </Button>
          </div>

          {/* Connection Status */}
          <div className="text-xs text-muted-foreground">
            <p>
              Connection Status: {navigator.onLine ? 'Online' : 'Offline'}
            </p>
            <p className="mt-1">
              This page will automatically refresh when your connection is restored.
            </p>
          </div>
        </div>
      </div>

      {/* Auto-refresh when connection is restored */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.addEventListener('online', function() {
              setTimeout(function() {
                window.location.reload();
              }, 1000);
            });
          `
        }}
      />
    </>
  );
}
