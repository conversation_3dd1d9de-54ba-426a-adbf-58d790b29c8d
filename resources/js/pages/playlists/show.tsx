import React, { useState, useCallback, useEffect } from 'react';
import { Head, Link, useForm, router } from '@inertiajs/react';
import { usePageRefresh } from '@/contexts/auto-refresh-context';
import { toast } from 'sonner';
import axios from 'axios';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import StreamDetailModal from '@/components/streams/stream-detail-modal';
import StreamIcon from '@/components/streams/stream-icon';
import {
  MoreVertical,
  Pencil,
  Trash2,
  Share2,
  Download,
  Play,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  GripVertical,
  Copy,
  ExternalLink,
  Edit,
  Globe,
  Lock,
  Layers,
  ChevronDown,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface Stream {
  id: number;
  name: string;
  url: string;
  type: string;
  status: string;
  tvg_logo: string | null;
  pivot: {
    position: number;
  };
  stream_cluster_id: number | null;
  cluster_priority?: number | null;
  cluster?: {
    id: number;
    name: string;
    status: string;
    normalized_name?: string;
    tvg_logo?: string | null;
    streams?: Stream[];
  } | null;
}

interface Playlist {
  id: number;
  name: string;
  description: string | null;
  is_public: boolean;
  share_token: string | null;
  created_at: string;
  updated_at: string;
  streams: Stream[];
}

interface AvailableStream {
  id: number;
  name: string;
  status: string;
  tvg_logo: string | null;
  stream_cluster_id: number | null;
  cluster_priority?: number | null;
  cluster?: {
    id: number;
    name: string;
    status: string;
    normalized_name?: string;
    tvg_logo?: string | null;
    streams?: AvailableStream[];
  } | null;
}

interface PlaylistShowProps extends PageProps {
  playlist: Playlist;
  shareUrl: string | null;
  exportUrl: string;
  rawUrl: string;
  availableStreams: AvailableStream[];
}

export default function PlaylistShow({ auth, playlist, shareUrl, exportUrl, rawUrl, availableStreams, errors }: PlaylistShowProps) {
  const [streams, setStreams] = useState(playlist.streams);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showAddStreamDialog, setShowAddStreamDialog] = useState(false);
  const [copied, setCopied] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [streamToRemove, setStreamToRemove] = useState<number | null>(null);
  const [isLastStream, setIsLastStream] = useState(false);
  const [selectedStream, setSelectedStream] = useState<Stream | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [doNotAskAgain, setDoNotAskAgain] = useState(false);
  const [doNotAskForThisPlaylist, setDoNotAskForThisPlaylist] = useState(false);
  const [doNotAskForOneHour, setDoNotAskForOneHour] = useState(false);
  const [expandedClusters, setExpandedClusters] = useState<number[]>([]);
  const [clusterStreams, setClusterStreams] = useState<Record<number, Stream[]>>({});

  const reorderForm = useForm({
    stream_ids: streams.map(stream => stream.id),
  });

  const addStreamForm = useForm({
    stream_id: '',
  });

  // Update local streams state when playlist prop changes (e.g., from auto-refresh)
  useEffect(() => {
    setStreams(playlist.streams);
  }, [playlist]);

  // Update the form data when streams change
  useEffect(() => {
    reorderForm.setData('stream_ids', streams.map(stream => stream.id));
  }, [streams]);

  const handleDragEnd = (result: any) => {
    console.log('Drag end result:', result);

    // Dropped outside the list
    if (!result.destination) {
      console.log('Dropped outside the list');
      return;
    }

    // Ensure we're not trying to drop in the same position
    if (
      result.destination.droppableId === result.source.droppableId &&
      result.destination.index === result.source.index
    ) {
      console.log('Dropped in the same position');
      return;
    }

    // Ensure we're handling the right droppable area
    if (result.destination.droppableId !== 'streams' || result.source.droppableId !== 'streams') {
      console.log('Not the streams droppable area');
      return;
    }

    const items = Array.from(streams);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    console.log('Reordered items:', items.map(item => item.id));

    // Update the local state
    setStreams(items);

    // Get the stream IDs in the new order
    const newStreamIds = items.map(stream => stream.id);
    console.log('New stream IDs:', newStreamIds);

    // Use router directly instead of form
    // Ensure stream_ids is properly formatted as an array
    const requestData = {
      stream_ids: newStreamIds
    };

    console.log('Sending request data:', requestData);

    router.post(route('playlists.reorder', playlist.id), requestData, {
      preserveScroll: true,
      preserveState: true,
      onBefore: () => {
        console.log('Before sending request');
      },
      onStart: () => {
        console.log('Request started');
      },
      onProgress: (progress) => {
        console.log('Request progress:', progress);
      },
      onSuccess: (page) => {
        // Success notification
        toast.success('Playlist order updated');
        console.log('Playlist reordered successfully', newStreamIds);
        console.log('Response:', page);
      },
      onError: (errors) => {
        console.error('Error reordering playlist:', errors);
        toast.error('Failed to reorder playlist');

        // Revert to the original order if there's an error
        setStreams(playlist.streams);
      },
      onFinish: () => {
        console.log('Request finished');
      }
    });
  };

  const handleRemoveStream = (streamId: number) => {
    // Check if this is the last stream in the playlist
    const isLast = streams.length === 1;
    setIsLastStream(isLast);
    setStreamToRemove(streamId);

    // Check if we should skip the confirmation dialog
    const doNotAskGlobal = localStorage.getItem('doNotAskStreamRemove');
    const doNotAskPlaylist = localStorage.getItem(`doNotAskStreamRemove_playlist_${playlist.id}`);
    const doNotAskHourly = localStorage.getItem('doNotAskStreamRemove_hourly');

    if (doNotAskGlobal === 'true') {
      handleRemoveConfirm();
      return;
    }

    if (doNotAskPlaylist === 'true') {
      handleRemoveConfirm();
      return;
    }

    if (doNotAskHourly) {
      const expiryTime = parseInt(doNotAskHourly);
      if (Date.now() < expiryTime) {
        handleRemoveConfirm();
        return;
      } else {
        // Clear expired hourly setting
        localStorage.removeItem('doNotAskStreamRemove_hourly');
      }
    }

    // Show the confirmation dialog
    setShowRemoveDialog(true);
  };

  const handleRemoveConfirm = () => {
    if (streamToRemove === null) return;

    // Save user preferences if any checkbox was checked
    if (doNotAskAgain) {
      localStorage.setItem('doNotAskStreamRemove', 'true');
    }

    if (doNotAskForThisPlaylist) {
      localStorage.setItem(`doNotAskStreamRemove_playlist_${playlist.id}`, 'true');
    }

    if (doNotAskForOneHour) {
      // Set expiry time to 1 hour from now
      const expiryTime = Date.now() + (60 * 60 * 1000);
      localStorage.setItem('doNotAskStreamRemove_hourly', expiryTime.toString());
    }

    // Use Inertia.js router to make a DELETE request
    router.delete(route('playlists.remove-stream', { playlist: playlist.id, stream: streamToRemove }), {}, {
      onSuccess: () => {
        // Remove the stream from the local state
        setStreams(streams.filter(stream => stream.id !== streamToRemove));

        // Update the stream_ids in the form
        reorderForm.setData('stream_ids',
          streams.filter(stream => stream.id !== streamToRemove).map(stream => stream.id)
        );

        // Close the dialog
        setShowRemoveDialog(false);
        setStreamToRemove(null);

        // Reset checkbox states
        setDoNotAskAgain(false);
        setDoNotAskForThisPlaylist(false);
        setDoNotAskForOneHour(false);
      },
      onError: (errors) => {
        console.error('Error removing stream:', errors);
        toast.error('Failed to remove stream from playlist');
        setShowRemoveDialog(false);
        setStreamToRemove(null);

        // Reset checkbox states
        setDoNotAskAgain(false);
        setDoNotAskForThisPlaylist(false);
        setDoNotAskForOneHour(false);
      }
    });
  };

  const handleAddStream = (e: React.FormEvent) => {
    e.preventDefault();

    addStreamForm.post(route('playlists.add-stream', playlist.id), {
      onSuccess: () => {
        setShowAddStreamDialog(false);
        addStreamForm.reset();
        // The page will refresh with the updated streams
      },
    });
  };

  const copyShareUrl = () => {
    if (shareUrl) {
      navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast.success('Share URL copied to clipboard');
    }
  };

  const copyRawUrl = () => {
    navigator.clipboard.writeText(rawUrl);
    toast.success('Raw M3U URL copied to clipboard');
  };

  const handleToggleVisibility = () => {
    router.patch(route('playlists.toggle-visibility', playlist.id), {}, {
      preserveScroll: true,
      onSuccess: () => {
        // The page will refresh with the updated playlist
      }
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Online</Badge>;
      case 'offline':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" /> Offline</Badge>;
      case 'error':
        return <Badge variant="warning" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Error</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const toggleClusterExpansion = (clusterId: number) => {
    if (expandedClusters.includes(clusterId)) {
      setExpandedClusters(expandedClusters.filter(id => id !== clusterId));
    } else {
      setExpandedClusters([...expandedClusters, clusterId]);
      fetchClusterStreams(clusterId);
    }
  };

  const fetchClusterStreams = async (clusterId: number) => {
    try {
      // Check if we already have the cluster streams
      if (clusterStreams[clusterId] && clusterStreams[clusterId].length > 0) {
        return;
      }

      const response = await axios.get(route('playlists.cluster-streams', clusterId));
      setClusterStreams(prev => ({
        ...prev,
        [clusterId]: response.data
      }));
    } catch (error) {
      console.error('Failed to fetch cluster streams', error);
      toast.error('Failed to load cluster streams');
    }
  };

  // Create a refresh handler for the playlist detail page
  const refreshPlaylist = useCallback(async () => {
    try {
      // Reset cluster streams when refreshing
      setClusterStreams({});

      // Use Inertia.js to reload the playlist data
      router.reload({
        only: ['playlist', 'availableStreams', 'shareUrl', 'exportUrl', 'rawUrl'],
      });
    } catch (error) {
      console.error('Failed to refresh playlist', error);
      throw error; // Re-throw to let the AutoRefreshContext handle it
    }
  }, []);

  // Register the refresh handler with the AutoRefreshContext
  usePageRefresh(refreshPlaylist);

  return (
    <AppLayout>
      <Head title={playlist.name} />

      <div className="py-4">
        <div className="w-full">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">{playlist.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                {playlist.is_public ? (
                  <Badge variant="success" className="flex items-center gap-1">
                    <Share2 className="h-3 w-3" /> Public
                  </Badge>
                ) : (
                  <Badge variant="secondary">Private</Badge>
                )}
                <Badge variant="outline">{streams.length} Streams</Badge>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button asChild variant="outline" size="sm">
                <Link href={route('playlists.edit', playlist.id)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit Details
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href={route('playlists.editor', playlist.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Advanced Editor
                </Link>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleVisibility}
              >
                {playlist.is_public ? (
                  <>
                    <Lock className="mr-2 h-4 w-4" />
                    Make Private
                  </>
                ) : (
                  <>
                    <Globe className="mr-2 h-4 w-4" />
                    Make Public
                  </>
                )}
              </Button>
              {playlist.is_public && (
                <Button variant="outline" size="sm" onClick={() => setShowShareDialog(true)}>
                  <Share2 className="mr-2 h-4 w-4" />
                  Share
                </Button>
              )}
              <Button asChild variant="outline" size="sm">
                <a href={exportUrl} download>
                  <Download className="mr-2 h-4 w-4" />
                  Export M3U
                </a>
              </Button>
            </div>
          </div>

          {playlist.description && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <p>{playlist.description}</p>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Streams</CardTitle>
                <CardDescription>
                  Drag and drop to reorder streams in the playlist.
                </CardDescription>
              </div>
              <Button onClick={() => setShowAddStreamDialog(true)}>
                Add Stream
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              {streams.length === 0 ? (
                <div className="p-6 text-center">
                  <p className="text-muted-foreground">No streams in this playlist yet.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => setShowAddStreamDialog(true)}
                  >
                    Add Stream
                  </Button>
                </div>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="streams">
                    {(provided) => (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead style={{ width: '40px' }}></TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead className="w-[100px]">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                        >
                          {streams.map((stream, index) => {
                            return (
                              <React.Fragment key={stream.id.toString()}>
                                <Draggable
                                  draggableId={stream.id.toString()}
                                  index={index}
                                  isDragDisabled={false}
                                >
                                  {(provided, snapshot) => (
                                    <TableRow
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      className={`cursor-pointer hover:bg-accent/50 ${snapshot.isDragging ? 'bg-accent shadow-lg' : ''}`}
                                      title="Click to view stream details"
                                      onClick={() => {
                                        setSelectedStream(stream);
                                        setModalOpen(true);
                                      }}
                                    >
                                      <TableCell {...provided.dragHandleProps} className="w-[40px] cursor-grab">
                                        <GripVertical className="h-4 w-4 text-muted-foreground" />
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center space-x-3">
                                          <StreamIcon
                                            logoUrl={stream.tvg_logo}
                                            name={stream.name}
                                          />
                                          <div>
                                            <div className="font-medium">{stream.name}</div>
                                            <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                                              {stream.url}
                                            </div>
                                            {stream.stream_cluster_id && (
                                              <div className="flex gap-1 mt-1">
                                                <Badge
                                                  variant="outline"
                                                  className="cursor-pointer flex items-center gap-1 hover:bg-secondary/50"
                                                  onClick={(e) => {
                                                    e.stopPropagation();
                                                    toggleClusterExpansion(stream.stream_cluster_id!);
                                                  }}
                                                >
                                                  <Layers className="h-3 w-3" />
                                                  Cluster
                                                  {expandedClusters.includes(stream.stream_cluster_id!) ? (
                                                    <ChevronDown className="h-3 w-3" />
                                                  ) : (
                                                    <ChevronRight className="h-3 w-3" />
                                                  )}
                                                </Badge>
                                                {stream.cluster?.streams && stream.cluster.streams.length > 0 &&
                                                 stream.cluster.streams[0].id !== stream.id && (
                                                  <Badge
                                                    variant="secondary"
                                                    className="text-xs flex items-center gap-1"
                                                  >
                                                    <RefreshCw className="h-3 w-3" />
                                                    Auto-updates to priority 1
                                                  </Badge>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </TableCell>
                                      <TableCell>{getStatusBadge(stream.status)}</TableCell>
                                      <TableCell>
                                        <Badge variant="outline">{stream.type.toUpperCase()}</Badge>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center space-x-2">
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            title="Play Stream"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              window.open(route('player.show', stream.id), '_blank');
                                            }}
                                          >
                                            <Play className="h-4 w-4" />
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            title="Remove from Playlist"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleRemoveStream(stream.id);
                                            }}
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </Draggable>

                                {stream.stream_cluster_id && expandedClusters.includes(stream.stream_cluster_id) && (
                                  <TableRow className="bg-muted/50">
                                    <TableCell colSpan={5} className="p-0">
                                      <div className="p-4">
                                        <div className="mb-2 flex items-center justify-between">
                                          <div className="flex items-center gap-2">
                                            <h4 className="text-sm font-semibold">Cluster: {stream.cluster?.name || stream.name}</h4>
                                            <Badge variant={stream.status === 'online' ? 'success' : 'destructive'}>
                                              {stream.status}
                                            </Badge>
                                          </div>
                                        </div>
                                        <Table>
                                          <TableHeader>
                                            <TableRow>
                                              <TableHead>Priority</TableHead>
                                              <TableHead>Name</TableHead>
                                              <TableHead>Status</TableHead>
                                              <TableHead>Type</TableHead>
                                              <TableHead>Actions</TableHead>
                                            </TableRow>
                                          </TableHeader>
                                          <TableBody>
                                            {clusterStreams[stream.stream_cluster_id]?.map(clusterStream => (
                                              <TableRow
                                                key={`cluster-${clusterStream.id}`}
                                                className="cursor-pointer hover:bg-secondary/10"
                                                onClick={() => {
                                                  setSelectedStream(clusterStream);
                                                  setModalOpen(true);
                                                }}
                                              >
                                                <TableCell>{clusterStream.cluster_priority || '-'}</TableCell>
                                                <TableCell>
                                                  <div className="flex items-center space-x-2">
                                                    <StreamIcon
                                                      logoUrl={clusterStream.tvg_logo}
                                                      name={clusterStream.name}
                                                      size="sm"
                                                    />
                                                    <span>{clusterStream.name}</span>
                                                  </div>
                                                </TableCell>
                                                <TableCell>{getStatusBadge(clusterStream.status)}</TableCell>
                                                <TableCell>{clusterStream.type}</TableCell>
                                                <TableCell>
                                                  <div className="flex items-center space-x-1">
                                                    <Button
                                                      variant="ghost"
                                                      size="icon"
                                                      className="h-7 w-7"
                                                      asChild
                                                      title="Play Stream"
                                                    >
                                                      <Link href={route('player.show', clusterStream.id)} target="_blank">
                                                        <Play className="h-4 w-4" />
                                                      </Link>
                                                    </Button>
                                                    <Button
                                                      variant="ghost"
                                                      size="icon"
                                                      className="h-7 w-7"
                                                      title="Add to Playlist"
                                                      onClick={(e) => {
                                                        e.stopPropagation();
                                                        addStreamForm.setData('stream_id', clusterStream.id.toString());
                                                        handleAddStream(e);
                                                      }}
                                                    >
                                                      <Plus className="h-4 w-4" />
                                                    </Button>
                                                  </div>
                                                </TableCell>
                                              </TableRow>
                                            ))}
                                          </TableBody>
                                        </Table>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                )}
                              </React.Fragment>
                            );
                          })}
                          {provided.placeholder}
                        </TableBody>
                      </Table>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Share Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share Playlist</DialogTitle>
            <DialogDescription>
              Anyone with this link can view and export this playlist.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center space-x-2">
            <Input
              value={shareUrl || ''}
              readOnly
              className="flex-1"
            />
            <Button size="icon" onClick={copyShareUrl}>
              {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          {shareUrl && (
            <div className="space-y-2 mt-2">
              <Button asChild variant="outline" className="w-full">
                <a href={shareUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Open Shared View
                </a>
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={copyRawUrl}
              >
                <Download className="mr-2 h-4 w-4" />
                Copy Raw M3U URL
              </Button>
              <div className="text-xs text-muted-foreground mt-1">
                Use the Raw M3U URL directly in your IPTV player
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowShareDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Stream Dialog */}
      <Dialog open={showAddStreamDialog} onOpenChange={setShowAddStreamDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Stream to Playlist</DialogTitle>
            <DialogDescription>
              Select a stream to add to this playlist.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddStream}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="stream_id">Stream</Label>
                <select
                  id="stream_id"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={addStreamForm.data.stream_id}
                  onChange={(e) => addStreamForm.setData('stream_id', e.target.value)}
                  required
                >
                  <option value="">Select a stream</option>
                  {availableStreams.length === 0 ? (
                    <option value="" disabled>No available streams</option>
                  ) : (
                    availableStreams.map((stream) => (
                      <option key={stream.id} value={stream.id}>
                        {stream.name} ({stream.status})
                      </option>
                    ))
                  )}
                </select>
                {errors.stream_id && (
                  <p className="text-sm text-red-500">{errors.stream_id}</p>
                )}
              </div>
            </div>
            <DialogFooter className="mt-4">
              <Button type="button" variant="outline" onClick={() => setShowAddStreamDialog(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={addStreamForm.processing}>
                Add to Playlist
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Remove Stream Dialog */}
      <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Stream</AlertDialogTitle>
            <AlertDialogDescription>
              {isLastStream
                ? 'Are you sure you want to remove this stream from the playlist? The playlist will remain, but will be empty.'
                : 'Are you sure you want to remove this stream from the playlist?'}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="flex flex-col gap-2 py-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="doNotAskAgain"
                checked={doNotAskAgain}
                onCheckedChange={(checked) => {
                  setDoNotAskAgain(checked === true);
                  // Uncheck other options if this one is checked
                  if (checked) {
                    setDoNotAskForThisPlaylist(false);
                    setDoNotAskForOneHour(false);
                  }
                }}
              />
              <Label htmlFor="doNotAskAgain">Do not ask again</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="doNotAskForThisPlaylist"
                checked={doNotAskForThisPlaylist}
                onCheckedChange={(checked) => {
                  setDoNotAskForThisPlaylist(checked === true);
                  // Uncheck other options if this one is checked
                  if (checked) {
                    setDoNotAskAgain(false);
                    setDoNotAskForOneHour(false);
                  }
                }}
              />
              <Label htmlFor="doNotAskForThisPlaylist">Do not ask for this playlist</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="doNotAskForOneHour"
                checked={doNotAskForOneHour}
                onCheckedChange={(checked) => {
                  setDoNotAskForOneHour(checked === true);
                  // Uncheck other options if this one is checked
                  if (checked) {
                    setDoNotAskAgain(false);
                    setDoNotAskForThisPlaylist(false);
                  }
                }}
              />
              <Label htmlFor="doNotAskForOneHour">Do not ask for 1 hour</Label>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setStreamToRemove(null);
              setDoNotAskAgain(false);
              setDoNotAskForThisPlaylist(false);
              setDoNotAskForOneHour(false);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleRemoveConfirm}>Remove</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Stream Detail Modal */}
      <StreamDetailModal
        stream={selectedStream}
        open={modalOpen}
        onOpenChange={setModalOpen}
      />
    </AppLayout>
  );
}
