import React, { useState } from 'react';
import { Head, Link } from '@inertiajs/react';
import { toast } from 'sonner';
import { PageProps } from '@/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import StreamIcon from '@/components/streams/stream-icon';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Download,
  ArrowLeft
} from 'lucide-react';

interface Stream {
  id: number;
  name: string;
  url: string;
  type: string;
  status: string;
  tvg_logo: string | null;
  pivot: {
    position: number;
  };
}

interface Playlist {
  id: number;
  name: string;
  description: string | null;
  is_public: boolean;
  share_token: string;
  created_at: string;
  updated_at: string;
  streams: Stream[];
}

interface SharedPlaylistProps extends PageProps {
  playlist: Playlist;
  exportUrl: string;
  rawUrl: string;
}

export default function SharedPlaylist({ playlist, exportUrl, rawUrl }: SharedPlaylistProps) {

  const copyRawUrl = () => {
    navigator.clipboard.writeText(rawUrl);
    toast.success('Raw M3U URL copied to clipboard');
  };
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'online':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Online</Badge>;
      case 'offline':
        return <Badge variant="destructive" className="flex items-center gap-1"><XCircle className="h-3 w-3" /> Offline</Badge>;
      case 'error':
        return <Badge variant="warning" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Error</Badge>;
      case 'pending':
        return <Badge variant="info" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Head title={`${playlist.name} - Shared Playlist`} />

      <div className="py-4 px-2 w-full max-w-7xl mx-auto">
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold">{playlist.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="success" className="flex items-center gap-1">
                  Shared Playlist
                </Badge>
                <Badge variant="outline">{playlist.streams.length} Streams</Badge>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button asChild variant="outline" size="sm">
                <a href={exportUrl} download>
                  <Download className="mr-2 h-4 w-4" />
                  Export M3U
                </a>
              </Button>
              <Button variant="outline" size="sm" onClick={copyRawUrl}>
                <Download className="mr-2 h-4 w-4" />
                Copy Raw M3U URL
              </Button>
            </div>
          </div>
        </div>

        {playlist.description && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <p>{playlist.description}</p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Streams</CardTitle>
            <CardDescription>
              Streams in this shared playlist.
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {playlist.streams.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-muted-foreground">This playlist has no streams.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Type</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {playlist.streams.map((stream) => (
                    <TableRow
                      key={stream.id}
                      className="hover:bg-accent/50"
                    >
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <StreamIcon
                            logoUrl={stream.tvg_logo}
                            name={stream.name}
                          />
                          <div>
                            <div className="font-medium">{stream.name}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(stream.status)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{stream.type.toUpperCase()}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
          <CardFooter className="flex justify-between py-4">
            <p className="text-sm text-muted-foreground">
              This is a shared playlist. You can export it as an M3U file or use the Raw M3U URL directly in your IPTV player.
            </p>
          </CardFooter>
        </Card>
      </div>


    </div>
  );
}
