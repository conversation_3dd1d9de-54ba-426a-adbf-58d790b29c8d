import React from 'react';
import { Head, useForm } from '@inertiajs/react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/main-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface Playlist {
  id: number;
  name: string;
  description: string | null;
  is_public: boolean;
}

interface PlaylistEditProps extends PageProps {
  playlist: Playlist;
}

export default function PlaylistEdit({ auth, playlist, errors }: PlaylistEditProps) {
  const form = useForm({
    name: playlist.name,
    description: playlist.description || '',
    is_public: playlist.is_public,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    form.put(route('playlists.update', playlist.id));
  };

  return (
    <AppLayout>
      <Head title={`Edit ${playlist.name}`} />

      <div className="py-4">
        <div className="w-full">
          <h1 className="text-2xl font-bold mb-6">Edit Playlist</h1>

          <Card>
            <CardHeader>
              <CardTitle>Playlist Details</CardTitle>
              <CardDescription>
                Update your playlist information.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={form.data.name}
                      onChange={(e) => form.setData('name', e.target.value)}
                      required
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={form.data.description}
                      onChange={(e) => form.setData('description', e.target.value)}
                      rows={4}
                    />
                    {errors.description && (
                      <p className="text-sm text-red-500">{errors.description}</p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_public"
                      checked={form.data.is_public}
                      onCheckedChange={(checked) => form.setData('is_public', checked)}
                    />
                    <Label htmlFor="is_public">Make playlist public</Label>
                  </div>
                  {form.data.is_public && (
                    <p className="text-sm text-muted-foreground">
                      Public playlists can be shared with anyone using a link, even if they don't have an account.
                    </p>
                  )}

                  {Object.keys(errors).length > 0 && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Please fix the errors above before submitting the form.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => window.history.back()}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={form.processing}>
                    {form.processing ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
