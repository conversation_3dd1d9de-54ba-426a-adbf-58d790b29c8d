export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
}

export interface Category {
  id: number;
  name: string;
  user_id: number;
}

export interface Tag {
  id: number;
  name: string;
  user_id: number;
}

export interface Stream {
  id: number;
  name: string;
  normalized_name: string;
  url: string;
  type: string;
  status: 'online' | 'offline' | 'pending' | 'error';
  user_id: number;
  tvg_id?: string;
  tvg_name?: string;
  tvg_logo?: string;
  group_title?: string;
  last_checked_at?: string;
  metadata?: any;
  exclude_from_export?: boolean;
  stream_cluster_id?: number;
  cluster_priority?: number;
  created_at: string;
  updated_at: string;
}

export interface StreamCluster {
  id: number;
  name: string;
  normalized_name: string;
  status: 'online' | 'offline';
  user_id: number;
  category_id?: number;
  tvg_logo?: string;
  category?: Category;
  streams_count?: number;
  created_at: string;
  updated_at: string;
}

export interface Playlist {
  id: number;
  name: string;
  user_id: number;
  is_public: boolean;
  share_token?: string;
  created_at: string;
  updated_at: string;
}

export interface M3UImport {
  id: number;
  name: string;
  source: string;
  is_url: boolean;
  user_id: number;
  status: string;
  metadata: any;
  created_at: string;
  updated_at: string;
  last_validated_at?: string;
}

export interface ExportJob {
  id: number;
  user_id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path?: string;
  metadata: any;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

export interface StreamSchedule {
  id: number;
  user_id: number;
  type: 'validation' | 'refetch' | 'both';
  interval_hours: number;
  is_active: boolean;
  metadata: any;
  next_run_at: string;
  created_at: string;
  updated_at: string;
}

export interface UrlListImport {
  id: number;
  user_id: number;
  name: string;
  url: string;
  is_active: boolean;
  interval_hours: number;
  next_run_at: string;
  created_at: string;
  updated_at: string;
}

export interface GitHubCredential {
  id: number;
  user_id: number;
  name: string;
  token_hint: string;
  created_at: string;
  updated_at: string;
}

export interface PreferredChannel {
  id: number;
  user_id: number;
  name: string;
  source: string;
  is_url: boolean;
  created_at: string;
  updated_at: string;
}
