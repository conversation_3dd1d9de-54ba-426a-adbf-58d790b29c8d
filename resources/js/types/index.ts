export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  profile_photo_url?: string;
}

export interface PageProps {
  auth: {
    user: User | null;
  };
  errors: Record<string, string>;
  flash?: {
    success?: string;
    error?: string;
    warning?: string;
    info?: string;
  };
}

export interface BreadcrumbItem {
  title: string;
  href: string;
  current?: boolean;
}
