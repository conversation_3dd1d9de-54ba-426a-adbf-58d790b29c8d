import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Create a mock StreamCard component
const MockStreamCard = ({
  stream,
  index,
  expandedClusters = [],
  onToggleCluster,
  clusterStreams = {},
  loadingClusters = [],
  onAddToPlaylist,
  onRemove,
}: any) => (
  <div data-testid="stream-card">
    <div>{stream.name}</div>
    {stream.stream_cluster_id && (
      <div data-testid="cluster-badge">Cluster</div>
    )}
    {stream.stream_cluster_id &&
     stream.cluster?.streams &&
     stream.cluster.streams.length > 0 &&
     stream.cluster.streams[0].id !== stream.id && (
      <div data-testid="auto-update-badge">Auto-updates to priority 1</div>
    )}
    {onRemove && (
      <button onClick={() => onRemove(stream.id)}>Remove</button>
    )}
    {onAddToPlaylist && (
      <button onClick={() => onAddToPlaylist(stream)}>Add</button>
    )}
  </div>
);

// Mock the route function
vi.mock('@inertiajs/react', () => ({
  Link: ({ children, ...props }: any) => <a {...props}>{children}</a>,
  router: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

describe('StreamCard Component', () => {
  const mockStream = {
    id: 1,
    name: 'Test Stream',
    url: 'http://example.com/stream',
    type: 'hls',
    status: 'online',
    tvg_logo: null,
    group_title: 'Test Group',
    stream_cluster_id: 1,
    cluster_priority: 2,
    cluster: {
      id: 1,
      name: 'Test Cluster',
      status: 'online',
      normalized_name: 'test cluster',
      tvg_logo: null,
      streams: [
        {
          id: 2,
          name: 'Priority 1 Stream',
          url: 'http://example.com/priority1',
          type: 'hls',
          status: 'online',
          tvg_logo: null,
          group_title: 'Test Group',
          stream_cluster_id: 1,
          cluster_priority: 1,
        },
      ],
    },
  };

  const mockStreamPriority1 = {
    ...mockStream,
    id: 2,
    name: 'Priority 1 Stream',
    cluster_priority: 1,
  };

  const mockStreamNoCluster = {
    id: 3,
    name: 'No Cluster Stream',
    url: 'http://example.com/noCluster',
    type: 'hls',
    status: 'online',
    tvg_logo: null,
    group_title: 'Test Group',
    stream_cluster_id: null,
    cluster_priority: null,
    cluster: null,
  };

  it('displays the stream name', () => {
    render(
      <MockStreamCard
        stream={mockStream}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.getByText('Test Stream')).toBeInTheDocument();
  });

  it('displays the cluster badge for streams in a cluster', () => {
    render(
      <MockStreamCard
        stream={mockStream}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.getByTestId('cluster-badge')).toBeInTheDocument();
  });

  it('does not display the cluster badge for streams not in a cluster', () => {
    render(
      <MockStreamCard
        stream={mockStreamNoCluster}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.queryByTestId('cluster-badge')).not.toBeInTheDocument();
  });

  it('displays the "auto updates to priority 1" badge for non-priority-1 streams in a cluster', () => {
    render(
      <MockStreamCard
        stream={mockStream}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.getByTestId('auto-update-badge')).toBeInTheDocument();
  });

  it('does not display the "auto updates to priority 1" badge for priority-1 streams in a cluster', () => {
    render(
      <MockStreamCard
        stream={mockStreamPriority1}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.queryByTestId('auto-update-badge')).not.toBeInTheDocument();
  });

  it('does not display the "auto updates to priority 1" badge for streams not in a cluster', () => {
    render(
      <MockStreamCard
        stream={mockStreamNoCluster}
        index={0}
        expandedClusters={[]}
        onToggleCluster={() => {}}
        clusterStreams={{}}
        loadingClusters={[]}
      />
    );

    expect(screen.queryByTestId('auto-update-badge')).not.toBeInTheDocument();
  });
});
