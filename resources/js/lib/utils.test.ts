import { cn } from './utils';
import { describe, expect, it } from 'vitest';

describe('cn (className utility)', () => {
  it('should merge class names correctly', () => {
    const result = cn('class1', 'class2');
    expect(result).toBe('class1 class2');
  });

  it('should handle conditional classes', () => {
    const result = cn('base', true && 'included', false && 'excluded');
    expect(result).toBe('base included');
  });

  it('should handle undefined and null values', () => {
    const result = cn('base', undefined, null, 'valid');
    expect(result).toBe('base valid');
  });

  it('should handle empty strings', () => {
    const result = cn('base', '', 'valid');
    expect(result).toBe('base valid');
  });

  it('should handle objects with class names as keys and booleans as values', () => {
    const result = cn('base', { conditional: true, excluded: false });
    expect(result).toContain('base');
    expect(result).toContain('conditional');
    expect(result).not.toContain('excluded');
  });

  it('should handle arrays of class names', () => {
    const result = cn('base', ['class1', 'class2']);
    expect(result).toBe('base class1 class2');
  });

  it('should handle complex combinations', () => {
    const result = cn(
      'base',
      { conditional: true, excluded: false },
      ['array1', 'array2'],
      undefined,
      null,
      '',
      'valid'
    );
    
    expect(result).toContain('base');
    expect(result).toContain('conditional');
    expect(result).not.toContain('excluded');
    expect(result).toContain('array1');
    expect(result).toContain('array2');
    expect(result).toContain('valid');
  });

  it('should handle tailwind class conflicts correctly', () => {
    const result = cn('text-red-500', 'text-blue-500');
    // tailwind-merge should resolve the conflict and keep the last one
    expect(result).toBe('text-blue-500');
  });

  it('should merge tailwind classes intelligently', () => {
    const result = cn('p-4 text-red-500', 'p-8 text-blue-500');
    // Should keep p-8 (last one) and text-blue-500 (last one)
    expect(result).toBe('p-8 text-blue-500');
  });
});
