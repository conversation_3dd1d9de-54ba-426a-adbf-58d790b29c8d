<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Stream Validation Settings
    |--------------------------------------------------------------------------
    |
    | These settings control how streams are validated in the application.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Validation Timeout
    |--------------------------------------------------------------------------
    |
    | The timeout in seconds for stream validation requests.
    | Increase this value if you have slow streams that take longer to respond.
    |
    */
    'validation_timeout' => env('STREAM_VALIDATION_TIMEOUT', 30),

    /*
    |--------------------------------------------------------------------------
    | Maximum Download Size
    |--------------------------------------------------------------------------
    |
    | The maximum number of bytes to download when validating a stream.
    | This prevents excessive data usage when validating large streams.
    | Default is 1MB (1048576 bytes).
    |
    */
    'validation_max_size' => env('STREAM_VALIDATION_MAX_SIZE', 1048576),

    /*
    |--------------------------------------------------------------------------
    | Validation Attempts
    |--------------------------------------------------------------------------
    |
    | The number of times to attempt validating a stream before marking it as error.
    |
    */
    'validation_attempts' => env('STREAM_VALIDATION_ATTEMPTS', 3),

    /*
    |--------------------------------------------------------------------------
    | Validation Retry Delay
    |--------------------------------------------------------------------------
    |
    | The initial delay in seconds between retry attempts.
    |
    */
    'validation_retry_delay' => env('STREAM_VALIDATION_RETRY_DELAY', 2),

    /*
    |--------------------------------------------------------------------------
    | Validation Retry Multiplier
    |--------------------------------------------------------------------------
    |
    | The multiplier for exponential backoff between retry attempts.
    |
    */
    'validation_retry_multiplier' => env('STREAM_VALIDATION_RETRY_MULTIPLIER', 2),
];
