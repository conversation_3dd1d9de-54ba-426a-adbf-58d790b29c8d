<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Stream Proxy Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the stream proxy service.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Enable Proxy
    |--------------------------------------------------------------------------
    |
    | Enable or disable the stream proxy functionality.
    |
    */
    'enabled' => env('STREAM_PROXY_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Proxy Timeout
    |--------------------------------------------------------------------------
    |
    | The timeout in seconds for proxy requests.
    |
    */
    'timeout' => env('STREAM_PROXY_TIMEOUT', 60),

    /*
    |--------------------------------------------------------------------------
    | Proxy URL Expiration
    |--------------------------------------------------------------------------
    |
    | The expiration time in hours for signed proxy URLs.
    |
    */
    'url_expiration' => env('STREAM_PROXY_URL_EXPIRATION', 6),

    /*
    |--------------------------------------------------------------------------
    | Proxy Cache
    |--------------------------------------------------------------------------
    |
    | Enable or disable caching of proxied content.
    |
    */
    'cache_enabled' => env('STREAM_PROXY_CACHE_ENABLED', true),

    /*
    |--------------------------------------------------------------------------
    | Cache TTL
    |--------------------------------------------------------------------------
    |
    | The time-to-live in seconds for cached proxy content.
    |
    */
    'cache_ttl' => env('STREAM_PROXY_CACHE_TTL', 300), // 5 minutes

    /*
    |--------------------------------------------------------------------------
    | Domains Requiring Proxy
    |--------------------------------------------------------------------------
    |
    | A list of domains that are known to require proxy for playback.
    |
    */
    'proxy_domains' => [
        'iptv-org.github.io',
        'raw.githubusercontent.com',
        'cdn.jsdelivr.net',
        'iptvcat.com',
        'iptv.live',
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-detect CORS Issues
    |--------------------------------------------------------------------------
    |
    | Automatically detect and use proxy for streams with CORS issues.
    |
    */
    'auto_detect_cors' => env('STREAM_PROXY_AUTO_DETECT_CORS', true),

    /*
    |--------------------------------------------------------------------------
    | Rewrite HLS Playlists
    |--------------------------------------------------------------------------
    |
    | Rewrite URLs in HLS playlists to use the proxy.
    |
    */
    'rewrite_hls' => env('STREAM_PROXY_REWRITE_HLS', true),

    /*
    |--------------------------------------------------------------------------
    | User Agent
    |--------------------------------------------------------------------------
    |
    | The User-Agent header to use for proxy requests.
    |
    */
    'user_agent' => env('STREAM_PROXY_USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'),
];
