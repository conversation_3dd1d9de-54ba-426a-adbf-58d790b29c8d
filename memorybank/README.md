# IPTV Manager Memory Bank

This directory contains streamlined documentation for the IPTV Manager project, serving as a comprehensive knowledge base for development and maintenance.

## Documentation Structure

### 📋 [Project Overview](project-overview.md)
Complete project description, core features, and technical architecture overview.

### 🚀 [Current State](current-state.md)
Current development focus, recent implementations, and active work areas.

### 🛠️ [Technical Documentation](technical-docs.md)
System architecture, database schema, API endpoints, and development environment setup.

### 🗺️ [Development Roadmap](roadmap.md)
Short, medium, and long-term goals with implementation priorities and success metrics.

### 📝 [Implementation Notes](implementation-notes.md)
Refactoring achievements, architecture decisions, optimizations, and technical considerations.

## Quick Reference

### Current Focus (May 2025)
- UI/UX enhancements with responsive design
- Advanced filtering and stream management
- Auto-refresh functionality and user preferences
- Widescreen layout optimization
- Predefined M3U sources integration

### Key Technologies
- **Backend**: Laravel 12, MySQL, Redis, Laravel Horizon
- **Frontend**: React/TypeScript, Inertia.js, Tailwind CSS, shadcn/ui
- **Streaming**: HLS.js with custom video controls
- **Queue Processing**: Background jobs with monitoring

### Architecture Patterns
- Repository pattern for data access
- Service layer for business logic
- Queue-based stream validation
- Component-based UI with reusable elements

## Maintenance Guidelines

Update this documentation when:
- **New Features**: Major functionality additions
- **Architecture Changes**: Significant system modifications  
- **Performance Improvements**: Optimization implementations
- **Bug Fixes**: Important issue resolutions
- **Planning Updates**: Roadmap or priority changes

## Getting Started

1. Read [Project Overview](project-overview.md) for system understanding
2. Check [Current State](current-state.md) for latest developments
3. Review [Technical Documentation](technical-docs.md) for implementation details
4. Consult [Roadmap](roadmap.md) for future planning
5. Reference [Implementation Notes](implementation-notes.md) for technical decisions

## Usage

Refer to these files when:

- Planning new features or changes
- Debugging issues
- Onboarding new team members
- Making architectural decisions
- Reviewing the project's progress
