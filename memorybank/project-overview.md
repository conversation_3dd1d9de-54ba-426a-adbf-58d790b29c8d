# IPTV Manager - Project Overview

## Problem Statement
IPTV users struggle with managing multiple streams and playlists, checking stream availability, and organizing content effectively. Current solutions lack comprehensive management features, reliable stream checking, and user-friendly interfaces.

## Project Description
IPTV Manager is a web application built with Laravel 12 and React/TypeScript that allows users to import, validate, manage, and stream M3U playlists. It serves as a central hub for IPTV stream management with advanced features for playlist organization and export.

## Core Features

### Stream & Playlist Management
- **Import M3U Playlists**: From URLs, file uploads, or bulk URL lists
- **Stream Validation**: Background validation for availability, quality, resolution, codec, and bitrate
- **Stream Organization**: Categories, tags, and advanced filtering/sorting
- **Playlist Editor**: Advanced drag-and-drop interface for stream management
- **Stream Clustering**: Group identical streams with automatic priority management

### Import & Export Capabilities
- **Multiple Import Methods**: 
  - Single/bulk URL imports
  - File uploads (single/multiple)
  - URL list imports with scheduling
- **Advanced Export Options**:
  - GitHub integration with repository/branch selection
  - Custom M3U/M3U8 playlist creation
  - Preferred channels support with JSON import
  - Duplicate channel skipping based on quality
  - Stream exclusion controls

### Monitoring & Automation
- **Scheduled Tasks**: Automatic refetch/revalidation, exports
- **Stream Status Tracking**: Real-time health monitoring
- **Background Processing**: Queue-based validation with Laravel Horizon
- **Auto-refresh System**: Application-wide with user preferences

### Playback & Streaming
- **Built-in Video Player**: HLS.js with custom controls
- **Quality Selection**: Adaptive bitrate streaming
- **Playback Controls**: Speed adjustment, keyboard shortcuts, fullscreen
- **Stream Details**: Modal overlays with comprehensive metadata

### User Experience
- **Responsive Design**: Optimized for all screen sizes
- **User Preferences**: Customizable playback, UI, and notification settings
- **Interactive Filtering**: Field-based filtering with chaining support
- **Bulk Operations**: Multi-stream actions with confirmation dialogs

## Technical Architecture
- **Backend**: Laravel 12 with queue processing, repository pattern
- **Frontend**: React/TypeScript with Inertia.js, Tailwind CSS, shadcn/ui
- **Database**: MySQL with optimized schema for stream management
- **Queue Management**: Laravel Horizon for background job processing
- **Stream Processing**: Multi-stage validation with configurable timeouts

## Key Design Decisions
- **Repository Pattern**: Abstraction layer for data access
- **Service Layer**: Business logic separation from controllers
- **Queue-based Processing**: Non-blocking stream validation
- **Component-based UI**: Reusable shadcn/ui components
- **JSON Metadata Storage**: Flexible stream metadata handling
- Merge multiple playlists with duplicate detection
- Export/backup your database of streams
- Migration tools from other IPTV management systems

## User Personas

### Casual IPTV User
- Wants to watch IPTV content with minimal technical knowledge
- Needs a simple way to import and use existing playlists
- Values in-browser streaming without additional software
- Prefers an intuitive interface for finding and watching content

### IPTV Enthusiast
- Manages multiple playlists and hundreds of streams
- Regularly checks stream availability and quality
- Creates and shares custom playlists
- Needs detailed metadata management and organization tools
- Wants to track and manage imported M3U playlists
- Needs to refetch and revalidate playlists periodically

### Content Manager
- Maintains playlists for multiple users or organizations
- Requires bulk operations and advanced management tools
- Values analytics and reporting features
- Needs reliable monitoring and notification systems
- Requires source tracking for imported streams
- Wants to manage playlist sources and keep them updated

## Technical Architecture
- **Backend**: Laravel 12.x with PHP 8.2+
- **Frontend**: React with TypeScript
- **Database**: MySQL/MariaDB
- **Queue System**: Laravel Queue with Redis
- **Containerization**: Docker with Laravel Sail
- **Authentication**: Laravel built-in authentication
- **UI Framework**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context API
- **Routing**: Inertia.js
- **Video Player**: HLS.js with custom controls

## Key Design Principles
- Each user has their own streams and playlists, even if identical to another user's
- All streams are proxied through the server to avoid CORS issues with m3u8 (HLS) and TS stream segments
- Lazy loading is implemented for large datasets (1000+ rows)
- Background processing for time-consuming operations
- Responsive design for all device sizes
- Consistent file naming conventions (kebab-case for components, lowercase for folders)
- Unified navigation with a single sidebar implementation
- Dedicated settings section with its own navigation
- Repository Pattern for data access
- Service Layer for business logic

## User Experience Goals

### Simplicity
- Clean, intuitive interface with direct action buttons and tooltips
- Progressive disclosure of advanced features
- Clear visual indicators for stream status and quality
- Streamlined workflows with consistent navigation
- Helpful visual feedback for interactive elements

### Reliability
- Accurate stream status information
- Consistent performance for background processing
- Dependable playlist generation and export
- Stable in-browser streaming experience

### Efficiency
- Quick access to frequently used streams with direct action buttons
- Smart search with responsive filtering capabilities
- Automated processes for routine tasks
- Stream detail modals for quick information access
- Direct streaming from source with automatic proxy fallback
- Bulk operations for streams and playlists with "Delete All" functionality
- Background processing for all operations to maintain UI responsiveness

### Flexibility
- Customizable organization system
- Multiple viewing and management options
- Support for various IPTV formats and standards
- Adaptable to different user needs and preferences
- Ability to manage imported playlists with refetch and revalidate options
- Option to remove playlists with or without associated streams

## Key Differentiators
- Comprehensive stream validation (not just availability, but quality metrics)
- Intelligent background processing using Laravel's queue system
- Seamless in-browser streaming experience with direct source streaming
- Advanced metadata management capabilities
- User-friendly interface with direct action buttons and tooltips
- Intelligent CORS handling with automatic proxy fallback
- Consistent navigation and user experience across all pages
- Complete M3U playlist import tracking and management
- Ability to refetch and revalidate imported playlists with clear status tracking
- Smart handling of stream associations with imported playlists
- Improved refetch process that marks existing streams as "updated" instead of "skipped"
- Accurate import status that only shows as "completed" when all streams are validated

## Success Metrics
- User retention and engagement
- Number of streams and playlists managed
- Stream validation accuracy and reliability
- System performance under load
- User satisfaction with interface and features

## Development Setup

### Prerequisites
- Docker and Docker Compose
- Node.js and npm
- Composer
- Git

### Local Development Environment
The project uses Laravel Sail for containerized development:

```bash
# Clone repository
git clone [repository-url]
cd iptv-manager

# Install PHP dependencies
composer install

# Set up environment
cp .env.example .env
php artisan key:generate

# Start Docker containers
./vendor/bin/sail up -d

# Install JavaScript dependencies
npm install

# Run migrations
./vendor/bin/sail artisan migrate

# Start development server
npm run dev
```

## Technical Constraints

### Stream Processing
- Need to handle potentially large M3U files (thousands of entries)
- Stream validation can be resource-intensive
- Some streams may have access restrictions or geo-blocking
- Various stream formats and protocols must be supported (HLS, RTMP, etc.)
- Stream validation may timeout for slow or large streams
- Need to limit download size during validation to prevent excessive resource usage

### Performance Considerations
- Background processing for stream validation to avoid UI blocking
- Pagination and lazy loading for large datasets
- Caching strategies for frequently accessed data
- Efficient database queries for stream filtering and searching
- Virtualized lists for rendering large datasets efficiently
- Strategic database indexing for frequently queried columns
- Eager loading to avoid N+1 query issues
- Redis caching for validation results and frequently accessed data
- Queue prioritization and job batching for efficient background processing
- Cursor-based pagination for very large datasets
- Code splitting and lazy loading for reduced initial load times
- Component optimization to prevent unnecessary re-renders

## Project Status
The application is under active development with core functionality implemented and additional features being added regularly.
