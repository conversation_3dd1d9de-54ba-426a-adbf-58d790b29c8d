# IPTV Manager - Technical Documentation

## System Architecture

### Application Stack
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  React Frontend │◄────┤  Laravel API    │◄────┤  Queue Workers  │
│  (Inertia.js)   │     │                 │     │                 │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │                        │
                               │                        │
                               ▼                        │
                        ┌─────────────────┐            │
                        │                 │            │
                        │   MySQL DB      │◄───────────┘
                        │                 │
                        └─────────────────┘
```

### Layered Architecture
1. **Presentation Layer**: Controllers, Views, React Components
2. **Service Layer**: Business Logic and External Integrations
3. **Repository Layer**: Data Access Abstraction
4. **Model Layer**: Eloquent Models and Relationships

## Database Schema

### Core Tables

#### Users
```sql
id, name, email, password, preferences, remember_token, created_at, updated_at
```
- **Preferences**: J<PERSON><PERSON> column for user customization (playback, UI, notifications)

#### Streams
```sql
id, user_id, imported_m3u_playlist_id, name, url, status, category, logo, 
epg_channel_id, metadata, exclude_from_export, last_checked_at, created_at, updated_at
```
- **Status**: enum('pending', 'online', 'offline', 'error')
- **Metadata**: <PERSON><PERSON><PERSON> column for stream details (quality, codec, bitrate, etc.)

#### M3U Imports
```sql
id, user_id, name, url, status, streams_count, imported_count, skipped_count, 
error_count, last_refetched_at, created_at, updated_at
```

#### Export Jobs
```sql
id, user_id, filename, status, file_path, github_repo, github_branch, github_path,
preferred_channels, system_data_priority, skip_duplicates, metadata, created_at, updated_at
```

#### Predefined M3U Sources
```sql
id, title, url, channel_count, description, category, logo, created_at, updated_at
```

#### URL List Imports
```sql
id, user_id, url, name, is_active, interval_hours, last_run_at, next_run_at, 
metadata, created_at, updated_at, deleted_at
```

### Relationships
- **User** → hasMany(Streams, M3UImports, ExportJobs, URLListImports)
- **M3UImport** → belongsTo(User), hasMany(Streams)
- **Stream** → belongsTo(User, M3UImport), belongsToMany(ExportJobs)

## API Endpoints

### Authentication
- `POST /login` - User authentication
- `POST /logout` - User logout
- `POST /register` - User registration
- `GET /user` - Get authenticated user

### Stream Management
- `GET /streams` - List streams with filtering/sorting
- `GET /streams/{id}` - Get specific stream details
- `POST /streams/validate` - Validate selected streams
- `POST /streams/validate-all` - Validate all user streams
- `DELETE /streams/{id}` - Delete stream
- `DELETE /streams/bulk-delete` - Delete multiple streams

### Playlist Management
- `GET /playlists` - List M3U imports
- `POST /playlists/import` - Import from URL
- `POST /playlists/upload` - Upload M3U file
- `POST /playlists/{id}/refetch` - Refetch playlist
- `POST /playlists/{id}/revalidate` - Revalidate all streams
- `PUT /playlists/{id}` - Update playlist details
- `DELETE /playlists/{id}` - Delete playlist

### Export Management
- `GET /exports` - List export jobs
- `POST /exports` - Create new export
- `GET /exports/{id}/download` - Download export file
- `DELETE /exports/{id}` - Delete export

### Settings & Preferences
- `GET /settings/preferences` - Get user preferences
- `PUT /settings/preferences` - Update user preferences

## Development Environment

### Requirements
- **PHP**: 8.2+
- **Laravel**: 12.x
- **Node.js**: 18+
- **MySQL**: 8.0+
- **Redis**: For queue processing

### Key Packages
#### Backend
- `laravel/horizon` - Queue monitoring
- `inertiajs/inertia-laravel` - SPA framework
- `spatie/laravel-permission` - Authorization
- `guzzlehttp/guzzle` - HTTP client

#### Frontend
- `@inertiajs/react` - Inertia React adapter
- `@radix-ui/react-*` - UI primitives
- `tailwindcss` - CSS framework
- `hls.js` - Video streaming
- `@dnd-kit/core` - Drag and drop

### Queue Configuration
```php
// config/queue.php
'connections' => [
    'redis' => [
        'streams' => [
            'connection' => 'default',
            'queue' => 'streams',
            'retry_after' => 300,
        ],
        'imports_delete' => [
            'connection' => 'default', 
            'queue' => 'imports_delete',
            'retry_after' => 600,
        ],
    ],
],
```

### Horizon Configuration
```php
// config/horizon.php
'environments' => [
    'production' => [
        'streams-worker' => [
            'connection' => 'redis',
            'queue' => ['streams'],
            'balance' => 'auto',
            'processes' => 3,
            'tries' => 3,
        ],
    ],
],
```

## Code Patterns

### Repository Pattern
```php
interface StreamRepositoryInterface 
{
    public function getByStatusesForUser(array $statuses, int $userId): Collection;
    public function updateStatus(int $streamId, string $status): bool;
}

class StreamRepository extends BaseRepository implements StreamRepositoryInterface
{
    use BuildsQueries, HandlesErrors;
    
    protected $model = Stream::class;
}
```

### Service Layer
```php
class StreamService
{
    public function __construct(
        private StreamRepositoryInterface $streamRepository,
        private HttpClient $httpClient,
        private ErrorHandler $errorHandler
    ) {}
    
    public function validateStream(Stream $stream): ValidationResult
    {
        // Business logic implementation
    }
}
```

### Queue Jobs
```php
class ValidateStreamJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public $queue = 'streams';
    public $tries = 3;
    public $timeout = 30;
}
```

## Security Considerations

### Authentication & Authorization
- Laravel Sanctum for API authentication
- Policy-based authorization for resource access
- CSRF protection for state-changing requests

### Data Protection
- Encrypted GitHub tokens in database
- Input validation and sanitization
- SQL injection prevention via Eloquent ORM

### Stream Validation Security
- Timeout limits to prevent resource exhaustion
- Download size limits for validation requests
- User isolation for all operations

## Performance Optimizations

### Database
- Indexed foreign keys and frequently queried columns
- JSON column optimization for metadata storage
- Soft deletes for data recovery

### Caching Strategy
- Redis for session and queue storage
- Database query caching for static data
- Application-level caching for computed results

### Frontend Optimization
- Component lazy loading
- Virtual scrolling for large lists
- LocalStorage for user preferences
- Debounced search inputs
