name: Run Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  frontend-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Check types
      run: npm run types

    - name: Lint
      run: npm run lint

    - name: Run frontend tests
      run: npm run test

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        directory: ./coverage
        flags: frontend

  backend-tests:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Set up PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, redis
        coverage: xdebug

    - name: Copy .env
      run: cp .env.example .env

    - name: Set environment variables
      run: |
        echo "DB_CONNECTION=mysql" >> .env
        echo "DB_HOST=127.0.0.1" >> .env
        echo "DB_PORT=3306" >> .env
        echo "DB_DATABASE=testing" >> .env
        echo "DB_USERNAME=root" >> .env
        echo "DB_PASSWORD=password" >> .env
        echo "REDIS_HOST=127.0.0.1" >> .env
        echo "REDIS_PORT=6379" >> .env

    - name: Get composer cache directory
      id: composer-cache
      run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

    - name: Cache composer dependencies
      uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install Composer dependencies
      run: composer install --no-interaction --prefer-dist

    - name: Generate application key
      run: php artisan key:generate

    - name: Run database migrations
      run: php artisan migrate --force

    - name: Run backend tests
      run: php artisan test --coverage

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        directory: ./coverage
        flags: backend
